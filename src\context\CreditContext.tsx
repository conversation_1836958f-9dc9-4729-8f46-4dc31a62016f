"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import useAuth from '@/app/hooks/useAuth';
import { getSchoolCredits } from '@/app/services/SchoolServices';
import { getSchoolSubscription } from '@/app/services/SubscriptionServices';
import { calculateSchoolCredits } from '@/app/services/CreditServices';
import { SchoolSubscriptionSchema } from '@/app/models/SchoolSubscriptionModel';

interface CreditContextType {
  // Crédits calculés basés sur les paiements d'étudiants (Credit.js)
  availableCredits: number;
  totalPaid: number;
  creditCount: number;

  // Crédits d'école existants (legacy)
  schoolCredits: number;
  schoolCreditsLoading: boolean;

  // Souscription (basée sur les crédits calculés)
  subscription: SchoolSubscriptionSchema | null;
  subscriptionLoading: boolean;

  // Actions
  refreshCredits: () => void;
  refreshAll: () => void;

  // État global
  loading: boolean;
  error: string | null;
}

const CreditContext = createContext<CreditContextType | undefined>(undefined);

interface CreditProviderProps {
  children: ReactNode;
}

export function CreditProvider({ children }: CreditProviderProps) {
  const { user } = useAuth();

  // États pour les crédits calculés basés sur Credit.js
  const [availableCredits, setAvailableCredits] = useState<number>(0);
  const [totalPaid, setTotalPaid] = useState<number>(0);
  const [creditCount, setCreditCount] = useState<number>(0);

  // États pour les crédits d'école existants (legacy)
  const [schoolCredits, setSchoolCredits] = useState<number>(0);
  const [schoolCreditsLoading, setSchoolCreditsLoading] = useState(false);

  // États pour la souscription
  const [subscription, setSubscription] = useState<SchoolSubscriptionSchema | null>(null);
  const [subscriptionLoading, setSubscriptionLoading] = useState(false);

  // État global
  const [error, setError] = useState<string | null>(null);

  const schoolId = user?.school_ids?.[0];

  // Fonction pour calculer les crédits basés sur les paiements d'étudiants
  const calculateCreditsFromPayments = async () => {
    if (!schoolId) return;

    try {
      setError(null);

      // Utiliser la fonction du service pour calculer les crédits
      const result = await calculateSchoolCredits(schoolId);

      setTotalPaid(result.totalPaid);
      setAvailableCredits(result.availableCredits);
      setCreditCount(result.creditCount);

      return result;
    } catch (err) {
      console.error('Error calculating credits from payments:', err);
      setError('Erreur lors du calcul des crédits');
      return { totalPaid: 0, availableCredits: 0, creditCount: 0 };
    }
  };

  // Fonction pour récupérer les crédits d'école existants (legacy)
  const fetchSchoolCredits = async () => {
    if (!schoolId) return;

    try {
      setSchoolCreditsLoading(true);
      setError(null);
      const credits = await getSchoolCredits(schoolId);
      setSchoolCredits(credits);
    } catch (err) {
      console.error('Error fetching school credits:', err);
      // Ne pas afficher d'erreur pour les crédits legacy
    } finally {
      setSchoolCreditsLoading(false);
    }
  };

  // Fonction pour récupérer la souscription basée sur les crédits calculés
  const fetchSubscription = async () => {
    if (!schoolId) return;

    try {
      setSubscriptionLoading(true);
      setError(null);
      const response = await getSchoolSubscription(schoolId);
      setSubscription(response.subscription);
    } catch (err) {
      console.error('Error fetching subscription:', err);
      // Si pas de souscription, on peut en créer une basée sur les crédits calculés
      setSubscription(null);
    } finally {
      setSubscriptionLoading(false);
    }
  };

  // Fonction pour rafraîchir tous les crédits
  const refreshCredits = async () => {
    await calculateCreditsFromPayments();
    await fetchSchoolCredits();
  };

  // Fonction pour rafraîchir tout (crédits + souscription)
  const refreshAll = async () => {
    await calculateCreditsFromPayments();
    await fetchSchoolCredits();
    await fetchSubscription();
  };

  // Charger les données au montage et quand l'utilisateur change
  useEffect(() => {
    if (schoolId) {
      refreshAll();
    }
  }, [schoolId]);

  const loading = schoolCreditsLoading || subscriptionLoading;

  const value: CreditContextType = {
    availableCredits,
    totalPaid,
    creditCount,
    schoolCredits,
    schoolCreditsLoading,
    subscription,
    subscriptionLoading,
    refreshCredits,
    refreshAll,
    loading,
    error
  };

  return (
    <CreditContext.Provider value={value}>
      {children}
    </CreditContext.Provider>
  );
}

// Hook pour utiliser le contexte
export function useCreditContext(): CreditContextType {
  const context = useContext(CreditContext);
  if (context === undefined) {
    throw new Error('useCreditContext must be used within a CreditProvider');
  }
  return context;
}

// Hook pour les crédits d'école (compatibilité avec l'existant)
export function useSchoolCredits() {
  const { schoolCredits, schoolCreditsLoading, refreshCredits, error } = useCreditContext();
  return {
    credits: schoolCredits,
    loading: schoolCreditsLoading,
    refresh: refreshCredits,
    error
  };
}

// Hook pour les crédits de souscription (basés sur les paiements)
export function useSubscriptionCredits() {
  const {
    availableCredits,
    totalPaid,
    creditCount,
    subscriptionLoading,
    subscription,
    refreshAll,
    error
  } = useCreditContext();

  return {
    credits: availableCredits,
    availableCredits,
    totalPaid,
    creditCount,
    loading: subscriptionLoading,
    subscription,
    refresh: refreshAll,
    refreshAll,
    error,
    hasCredits: (amount: number = 1) => availableCredits >= amount,
    isLowBalance: subscription ? availableCredits <= subscription.low_credit_threshold : availableCredits <= 10
  };
}
