const fapshi = require('../utils/fapshi');
const CreditPurchase = require('../models/CreditPurchase');
const SchoolSubscription = require('../models/SchoolSubscription');
const School = require('../models/School');
const User = require('../models/User');
const PaymentFailureService = require('../services/paymentFailureService');
const NotificationService = require('../services/notificationService');

/**
 * Gestionnaire principal des webhooks Fapshi
 * Traite les notifications de changement de statut de paiement
 */
const handleFapshiWebhook = async (req, res) => {
  try {
    console.log('🔔 Webhook Fapshi reçu:', {
      body: req.body,
      headers: req.headers,
      timestamp: new Date().toISOString()
    });

    // Valider le webhook avec Fapshi
    const validation = await fapshi.validateWebhook(req.body);

    if (!validation.valid) {
      console.error('❌ Webhook invalide:', validation.message);
      return res.status(400).json({ 
        message: 'Invalid webhook',
        error: validation.message 
      });
    }

    const transaction = validation.transaction;
    console.log('✅ Webhook validé pour transaction:', transaction.transId);

    // Traiter selon le statut
    switch (transaction.status) {
      case 'SUCCESSFUL':
        await handleSuccessfulPayment(transaction);
        break;
      case 'FAILED':
        await handleFailedPayment(transaction);
        break;
      case 'EXPIRED':
        await handleExpiredPayment(transaction);
        break;
      default:
        console.log('ℹ️ Statut intermédiaire:', transaction.status);
    }

    // Répondre rapidement à Fapshi
    res.status(200).json({ 
      message: 'Webhook processed successfully',
      transId: transaction.transId,
      status: transaction.status
    });

  } catch (error) {
    console.error('❌ Erreur lors du traitement du webhook:', error);
    res.status(500).json({ 
      message: 'Webhook processing failed',
      error: error.message 
    });
  }
};

/**
 * Traiter un paiement réussi
 */
const handleSuccessfulPayment = async (transaction) => {
  try {
    console.log('💰 Traitement paiement réussi:', transaction.transId);

    // Trouver l'achat de crédit correspondant
    const creditPurchase = await CreditPurchase.findOne({
      $or: [
        { 'payment_gateway_response.transId': transaction.transId },
        { transaction_id: transaction.transId },
        { purchase_id: transaction.externalId }
      ]
    }).populate('school_id purchased_by');

    if (!creditPurchase) {
      console.error('❌ Achat de crédit non trouvé pour:', transaction.transId);
      return;
    }

    // Vérifier si déjà traité
    if (creditPurchase.payment_status === 'completed') {
      console.log('ℹ️ Paiement déjà traité:', transaction.transId);
      return;
    }

    // Marquer comme complété
    await creditPurchase.markAsCompleted(transaction);

    // Ajouter les crédits à l'école
    const schoolSubscription = await SchoolSubscription.findById(creditPurchase.subscription_id);
    if (schoolSubscription) {
      schoolSubscription.credits_balance += creditPurchase.credits_purchased;
      await schoolSubscription.save();

      console.log(`✅ ${creditPurchase.credits_purchased} crédits ajoutés à l'école ${creditPurchase.school_id}`);
    }

    // Envoyer notifications de confirmation (email + SMS)
    await NotificationService.sendPaymentSuccessNotification({
      email: creditPurchase.purchaser_email,
      phone: creditPurchase.billing_info?.phone,
      userName: creditPurchase.purchased_by?.name || creditPurchase.billing_info?.name,
      schoolName: creditPurchase.school_id?.name,
      creditsAmount: creditPurchase.credits_purchased,
      totalAmount: creditPurchase.total_amount,
      currency: creditPurchase.currency,
      transactionId: transaction.transId,
      purchaseDate: new Date().toLocaleDateString('fr-FR')
    });

    console.log('🎉 Paiement traité avec succès:', transaction.transId);

  } catch (error) {
    console.error('❌ Erreur lors du traitement du paiement réussi:', error);
    throw error;
  }
};

/**
 * Traiter un paiement échoué
 */
const handleFailedPayment = async (transaction) => {
  try {
    console.log('💔 Traitement paiement échoué:', transaction.transId);

    // Trouver l'achat de crédit correspondant
    const creditPurchase = await CreditPurchase.findOne({
      $or: [
        { 'payment_gateway_response.transId': transaction.transId },
        { transaction_id: transaction.transId },
        { purchase_id: transaction.externalId }
      ]
    }).populate('school_id purchased_by');

    if (!creditPurchase) {
      console.error('❌ Achat de crédit non trouvé pour:', transaction.transId);
      return;
    }

    // Marquer comme échoué
    await creditPurchase.markAsFailed(`Payment failed: ${transaction.status}`);

    // Envoyer notifications d'échec (email + SMS)
    await NotificationService.sendPaymentFailureNotification({
      email: creditPurchase.purchaser_email,
      phone: creditPurchase.billing_info?.phone,
      userName: creditPurchase.purchased_by?.name || creditPurchase.billing_info?.name,
      schoolName: creditPurchase.school_id?.name,
      creditsAmount: creditPurchase.credits_purchased,
      totalAmount: creditPurchase.total_amount,
      currency: creditPurchase.currency,
      transactionId: transaction.transId,
      failureReason: `Paiement échoué: ${transaction.status}`,
      canRetry: true,
      retryUrl: `${process.env.FRONTEND_URL}/school-admin/buy-credit`
    });

    console.log('📧 Notification d\'échec envoyée pour:', transaction.transId);

  } catch (error) {
    console.error('❌ Erreur lors du traitement du paiement échoué:', error);
    throw error;
  }
};

/**
 * Traiter un paiement expiré
 */
const handleExpiredPayment = async (transaction) => {
  try {
    console.log('⏰ Traitement paiement expiré:', transaction.transId);

    // Trouver l'achat de crédit correspondant
    const creditPurchase = await CreditPurchase.findOne({
      $or: [
        { 'payment_gateway_response.transId': transaction.transId },
        { transaction_id: transaction.transId },
        { purchase_id: transaction.externalId }
      ]
    }).populate('school_id purchased_by');

    if (!creditPurchase) {
      console.error('❌ Achat de crédit non trouvé pour:', transaction.transId);
      return;
    }

    // Marquer comme expiré
    await creditPurchase.markAsFailed('Payment link expired');

    // Envoyer notifications d'expiration (email + SMS)
    await NotificationService.sendPaymentExpirationNotification({
      email: creditPurchase.purchaser_email,
      phone: creditPurchase.billing_info?.phone,
      userName: creditPurchase.purchased_by?.name || creditPurchase.billing_info?.name,
      schoolName: creditPurchase.school_id?.name,
      creditsAmount: creditPurchase.credits_purchased,
      totalAmount: creditPurchase.total_amount,
      currency: creditPurchase.currency,
      transactionId: transaction.transId
    });

    console.log('📧 Notification d\'expiration envoyée pour:', transaction.transId);

  } catch (error) {
    console.error('❌ Erreur lors du traitement du paiement expiré:', error);
    throw error;
  }
};

// Fonction supprimée - remplacée par NotificationService.sendPaymentSuccessNotification

// Fonction supprimée - remplacée par NotificationService.sendPaymentFailureNotification

// Fonction supprimée - remplacée par NotificationService.sendPaymentExpirationNotification

/**
 * Endpoint pour tester les webhooks (développement uniquement)
 */
const testWebhook = async (req, res) => {
  if (process.env.NODE_ENV === 'production') {
    return res.status(403).json({ message: 'Test endpoint not available in production' });
  }

  try {
    const { transId, status = 'SUCCESSFUL' } = req.body;

    if (!transId) {
      return res.status(400).json({ message: 'transId is required' });
    }

    // Simuler un webhook
    const mockWebhookData = {
      transId,
      status,
      amount: 3000,
      email: '<EMAIL>',
      externalId: 'test_purchase_123',
      dateInitiated: new Date().toISOString(),
      dateConfirmed: new Date().toISOString()
    };

    // Traiter le webhook simulé
    req.body = mockWebhookData;
    await handleFapshiWebhook(req, res);

  } catch (error) {
    console.error('❌ Erreur test webhook:', error);
    res.status(500).json({ message: 'Test webhook failed', error: error.message });
  }
};

/**
 * Signaler un échec de paiement
 */
const reportPaymentFailure = async (req, res) => {
  try {
    const failureData = req.body;

    // Valider les données d'échec
    if (!failureData.transactionId || !failureData.reason) {
      return res.status(400).json({
        success: false,
        message: 'Transaction ID et raison d\'échec requis'
      });
    }

    // Traiter l'échec avec le service
    const result = await PaymentFailureService.handlePaymentFailure(
      failureData.transactionId,
      failureData.reason,
      failureData
    );

    res.json({
      success: true,
      message: 'Échec de paiement signalé avec succès',
      data: result
    });

  } catch (error) {
    console.error('❌ Erreur signalement échec:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors du signalement de l\'échec',
      error: error.message
    });
  }
};

/**
 * Tenter une reprise de paiement
 */
const retryPayment = async (req, res) => {
  try {
    const { transId } = req.params;

    if (!transId) {
      return res.status(400).json({
        success: false,
        message: 'ID de transaction requis'
      });
    }

    // Trouver l'achat de crédit
    const creditPurchase = await CreditPurchase.findOne({
      $or: [
        { transaction_id: transId },
        { 'payment_gateway_response.transId': transId }
      ]
    });

    if (!creditPurchase) {
      return res.status(404).json({
        success: false,
        message: 'Transaction non trouvée'
      });
    }

    // Vérifier le statut actuel avec Fapshi
    const currentStatus = await fapshi.paymentStatus(transId);

    if (currentStatus.statusCode === 200) {
      if (currentStatus.status === 'SUCCESSFUL') {
        // Le paiement a finalement réussi
        await handleSuccessfulPayment({ transId, ...currentStatus });

        res.json({
          success: true,
          message: 'Paiement finalement réussi',
          status: 'completed',
          data: currentStatus
        });
      } else if (currentStatus.status === 'FAILED') {
        res.json({
          success: false,
          message: 'Paiement toujours échoué',
          status: 'still_failed',
          data: currentStatus
        });
      } else {
        res.json({
          success: true,
          message: 'Paiement toujours en cours',
          status: 'pending',
          data: currentStatus
        });
      }
    } else {
      throw new Error('Impossible de vérifier le statut du paiement');
    }

  } catch (error) {
    console.error('❌ Erreur retry paiement:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la tentative de reprise',
      error: error.message
    });
  }
};

/**
 * Obtenir les statistiques des échecs de paiement
 */
const getFailureStatistics = async (req, res) => {
  try {
    const { startDate, endDate, schoolId } = req.query;

    const filters = {};
    if (startDate) filters.startDate = startDate;
    if (endDate) filters.endDate = endDate;
    if (schoolId) filters.schoolId = schoolId;

    const statistics = await PaymentFailureService.getFailureStatistics(filters);

    res.json({
      success: true,
      message: 'Statistiques récupérées avec succès',
      data: statistics
    });

  } catch (error) {
    console.error('❌ Erreur statistiques échecs:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des statistiques',
      error: error.message
    });
  }
};

module.exports = {
  handleFapshiWebhook,
  testWebhook,
  handleSuccessfulPayment,
  handleFailedPayment,
  handleExpiredPayment,
  reportPaymentFailure,
  retryPayment,
  getFailureStatistics
};
