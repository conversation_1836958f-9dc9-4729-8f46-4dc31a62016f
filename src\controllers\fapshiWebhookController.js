const fapshi = require('../utils/fapshi');
const CreditPurchase = require('../models/CreditPurchase');
const SchoolSubscription = require('../models/SchoolSubscription');
const School = require('../models/School');
const User = require('../models/User');
const { sendEmail } = require('../utils/emailService');

/**
 * Gestionnaire principal des webhooks Fapshi
 * Traite les notifications de changement de statut de paiement
 */
const handleFapshiWebhook = async (req, res) => {
  try {
    console.log('🔔 Webhook Fapshi reçu:', {
      body: req.body,
      headers: req.headers,
      timestamp: new Date().toISOString()
    });

    // Valider le webhook avec Fapshi
    const validation = await fapshi.validateWebhook(req.body);

    if (!validation.valid) {
      console.error('❌ Webhook invalide:', validation.message);
      return res.status(400).json({ 
        message: 'Invalid webhook',
        error: validation.message 
      });
    }

    const transaction = validation.transaction;
    console.log('✅ Webhook validé pour transaction:', transaction.transId);

    // Traiter selon le statut
    switch (transaction.status) {
      case 'SUCCESSFUL':
        await handleSuccessfulPayment(transaction);
        break;
      case 'FAILED':
        await handleFailedPayment(transaction);
        break;
      case 'EXPIRED':
        await handleExpiredPayment(transaction);
        break;
      default:
        console.log('ℹ️ Statut intermédiaire:', transaction.status);
    }

    // Répondre rapidement à Fapshi
    res.status(200).json({ 
      message: 'Webhook processed successfully',
      transId: transaction.transId,
      status: transaction.status
    });

  } catch (error) {
    console.error('❌ Erreur lors du traitement du webhook:', error);
    res.status(500).json({ 
      message: 'Webhook processing failed',
      error: error.message 
    });
  }
};

/**
 * Traiter un paiement réussi
 */
const handleSuccessfulPayment = async (transaction) => {
  try {
    console.log('💰 Traitement paiement réussi:', transaction.transId);

    // Trouver l'achat de crédit correspondant
    const creditPurchase = await CreditPurchase.findOne({
      $or: [
        { 'payment_gateway_response.transId': transaction.transId },
        { transaction_id: transaction.transId },
        { purchase_id: transaction.externalId }
      ]
    }).populate('school_id purchased_by');

    if (!creditPurchase) {
      console.error('❌ Achat de crédit non trouvé pour:', transaction.transId);
      return;
    }

    // Vérifier si déjà traité
    if (creditPurchase.payment_status === 'completed') {
      console.log('ℹ️ Paiement déjà traité:', transaction.transId);
      return;
    }

    // Marquer comme complété
    await creditPurchase.markAsCompleted(transaction);

    // Ajouter les crédits à l'école
    const schoolSubscription = await SchoolSubscription.findById(creditPurchase.subscription_id);
    if (schoolSubscription) {
      schoolSubscription.credits_balance += creditPurchase.credits_purchased;
      await schoolSubscription.save();

      console.log(`✅ ${creditPurchase.credits_purchased} crédits ajoutés à l'école ${creditPurchase.school_id}`);
    }

    // Envoyer notification par email
    await sendPaymentConfirmationEmail(creditPurchase, transaction);

    console.log('🎉 Paiement traité avec succès:', transaction.transId);

  } catch (error) {
    console.error('❌ Erreur lors du traitement du paiement réussi:', error);
    throw error;
  }
};

/**
 * Traiter un paiement échoué
 */
const handleFailedPayment = async (transaction) => {
  try {
    console.log('💔 Traitement paiement échoué:', transaction.transId);

    // Trouver l'achat de crédit correspondant
    const creditPurchase = await CreditPurchase.findOne({
      $or: [
        { 'payment_gateway_response.transId': transaction.transId },
        { transaction_id: transaction.transId },
        { purchase_id: transaction.externalId }
      ]
    }).populate('school_id purchased_by');

    if (!creditPurchase) {
      console.error('❌ Achat de crédit non trouvé pour:', transaction.transId);
      return;
    }

    // Marquer comme échoué
    await creditPurchase.markAsFailed(`Payment failed: ${transaction.status}`);

    // Envoyer notification d'échec
    await sendPaymentFailureEmail(creditPurchase, transaction);

    console.log('📧 Notification d\'échec envoyée pour:', transaction.transId);

  } catch (error) {
    console.error('❌ Erreur lors du traitement du paiement échoué:', error);
    throw error;
  }
};

/**
 * Traiter un paiement expiré
 */
const handleExpiredPayment = async (transaction) => {
  try {
    console.log('⏰ Traitement paiement expiré:', transaction.transId);

    // Trouver l'achat de crédit correspondant
    const creditPurchase = await CreditPurchase.findOne({
      $or: [
        { 'payment_gateway_response.transId': transaction.transId },
        { transaction_id: transaction.transId },
        { purchase_id: transaction.externalId }
      ]
    }).populate('school_id purchased_by');

    if (!creditPurchase) {
      console.error('❌ Achat de crédit non trouvé pour:', transaction.transId);
      return;
    }

    // Marquer comme expiré
    await creditPurchase.markAsFailed('Payment link expired');

    // Envoyer notification d'expiration
    await sendPaymentExpirationEmail(creditPurchase, transaction);

    console.log('📧 Notification d\'expiration envoyée pour:', transaction.transId);

  } catch (error) {
    console.error('❌ Erreur lors du traitement du paiement expiré:', error);
    throw error;
  }
};

/**
 * Envoyer email de confirmation de paiement
 */
const sendPaymentConfirmationEmail = async (creditPurchase, transaction) => {
  try {
    const school = creditPurchase.school_id;
    const user = creditPurchase.purchased_by;

    const emailData = {
      to: creditPurchase.purchaser_email,
      subject: '✅ Confirmation d\'achat de crédits - Scholarify',
      template: 'payment-confirmation',
      data: {
        userName: user?.name || creditPurchase.billing_info?.name || 'Cher client',
        schoolName: school?.name || 'Votre école',
        creditsAmount: creditPurchase.credits_purchased,
        totalAmount: creditPurchase.total_amount,
        currency: creditPurchase.currency,
        transactionId: transaction.transId,
        purchaseDate: new Date().toLocaleDateString('fr-FR'),
        dashboardUrl: `${process.env.FRONTEND_URL}/school-admin/buy-credit`
      }
    };

    await sendEmail(emailData);
    console.log('📧 Email de confirmation envoyé à:', creditPurchase.purchaser_email);

  } catch (error) {
    console.error('❌ Erreur envoi email confirmation:', error);
    // Ne pas faire échouer le webhook pour un problème d'email
  }
};

/**
 * Envoyer email d'échec de paiement
 */
const sendPaymentFailureEmail = async (creditPurchase, transaction) => {
  try {
    const school = creditPurchase.school_id;
    const user = creditPurchase.purchased_by;

    const emailData = {
      to: creditPurchase.purchaser_email,
      subject: '❌ Échec de paiement - Scholarify',
      template: 'payment-failure',
      data: {
        userName: user?.name || creditPurchase.billing_info?.name || 'Cher client',
        schoolName: school?.name || 'Votre école',
        creditsAmount: creditPurchase.credits_purchased,
        totalAmount: creditPurchase.total_amount,
        currency: creditPurchase.currency,
        transactionId: transaction.transId,
        retryUrl: `${process.env.FRONTEND_URL}/school-admin/buy-credit`
      }
    };

    await sendEmail(emailData);
    console.log('📧 Email d\'échec envoyé à:', creditPurchase.purchaser_email);

  } catch (error) {
    console.error('❌ Erreur envoi email échec:', error);
  }
};

/**
 * Envoyer email d'expiration de paiement
 */
const sendPaymentExpirationEmail = async (creditPurchase, transaction) => {
  try {
    const school = creditPurchase.school_id;
    const user = creditPurchase.purchased_by;

    const emailData = {
      to: creditPurchase.purchaser_email,
      subject: '⏰ Lien de paiement expiré - Scholarify',
      template: 'payment-expiration',
      data: {
        userName: user?.name || creditPurchase.billing_info?.name || 'Cher client',
        schoolName: school?.name || 'Votre école',
        creditsAmount: creditPurchase.credits_purchased,
        totalAmount: creditPurchase.total_amount,
        currency: creditPurchase.currency,
        transactionId: transaction.transId,
        newPurchaseUrl: `${process.env.FRONTEND_URL}/school-admin/buy-credit`
      }
    };

    await sendEmail(emailData);
    console.log('📧 Email d\'expiration envoyé à:', creditPurchase.purchaser_email);

  } catch (error) {
    console.error('❌ Erreur envoi email expiration:', error);
  }
};

/**
 * Endpoint pour tester les webhooks (développement uniquement)
 */
const testWebhook = async (req, res) => {
  if (process.env.NODE_ENV === 'production') {
    return res.status(403).json({ message: 'Test endpoint not available in production' });
  }

  try {
    const { transId, status = 'SUCCESSFUL' } = req.body;

    if (!transId) {
      return res.status(400).json({ message: 'transId is required' });
    }

    // Simuler un webhook
    const mockWebhookData = {
      transId,
      status,
      amount: 3000,
      email: '<EMAIL>',
      externalId: 'test_purchase_123',
      dateInitiated: new Date().toISOString(),
      dateConfirmed: new Date().toISOString()
    };

    // Traiter le webhook simulé
    req.body = mockWebhookData;
    await handleFapshiWebhook(req, res);

  } catch (error) {
    console.error('❌ Erreur test webhook:', error);
    res.status(500).json({ message: 'Test webhook failed', error: error.message });
  }
};

module.exports = {
  handleFapshiWebhook,
  testWebhook,
  handleSuccessfulPayment,
  handleFailedPayment,
  handleExpiredPayment
};
