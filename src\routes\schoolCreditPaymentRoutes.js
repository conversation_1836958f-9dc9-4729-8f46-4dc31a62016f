// routes/schoolCreditPaymentRoutes.js
const express = require('express');
const schoolCreditPaymentController = require('../controllers/schoolCreditPaymentController');
const { authenticate, authorize } = require('../middleware/middleware');
const { checkCreditManagementPermissions } = require('../middleware/financeMiddleware');

const router = express.Router();

// Routes pour les paiements de crédits d'école

/**
 * POST /school-credit-payment/initiate
 * Initier un paiement d'achat de crédits pour une école
 * Accessible par: admin, super, school_admin, bursar, ou staff avec permissions financières
 */
router.post('/initiate',
  authenticate,
  authorize(['admin', 'super', 'school_admin', 'bursar', 'teacher', 'dean_of_studies']),
  checkCreditManagementPermissions,
  schoolCreditPaymentController.initiateSchoolCreditPayment
);

/**
 * GET /school-credit-payment/status/:transaction_id
 * Vérifier le statut d'un paiement de crédit d'école
 * Accessible par: admin, super, school_admin, bursar, ou staff avec permissions financières
 */
router.get('/status/:transaction_id',
  authenticate,
  authorize(['admin', 'super', 'school_admin', 'bursar', 'teacher', 'dean_of_studies']),
  schoolCreditPaymentController.checkSchoolCreditPaymentStatus
);

/**
 * POST /school-credit-payment/webhook
 * Webhook pour les confirmations de paiement Fapshi
 * Pas d'authentification requise car c'est un webhook externe
 */
router.post('/webhook',
  schoolCreditPaymentController.handleSchoolCreditWebhook
);

module.exports = router;
