const express = require('express');
const router = express.Router();
const {
  handleWebhook,
  initiatePayment,
  checkPaymentStatus,
  searchTransactions,
  getServiceBalance,
  expireTransaction
} = require('../controllers/paymentController');

const {
  handleFapshiWebhook,
  testWebhook,
  reportPaymentFailure,
  retryPayment,
  getFailureStatistics
} = require('../controllers/fapshiWebhookController');


const { authenticate, authorize } = require('../middleware/middleware');
/**
 * @route POST /api/fapshi/initiate-payment
 * @desc Initier un paiement Fapshi
 * @access Private (Parent, Admin)
 */
router.post('/initiate-payment', 
  authenticate, 
  authorize(['parent', 'admin']), 
  initiatePayment
);

/**
 * @route GET /api/fapshi/payment-status/:transId
 * @desc Vérifier le statut d'un paiement
 * @access Private (Parent, Admin)
 */
router.get('/payment-status/:transId', 
  authenticate, 
  authorize(['parent', 'admin']), 
  checkPaymentStatus
);

/**
 * @route POST /api/fapshi/expire-transaction/:transId
 * @desc Expirer une transaction en attente
 * @access Private (Parent, Admin)
 */
router.post('/expire-transaction/:transId', 
  authenticate, 
  authorize(['parent', 'admin']), 
  expireTransaction
);

/**
 * @route GET /api/fapshi/search-transactions
 * @desc Rechercher des transactions avec filtres
 * @access Private (Admin only)
 */
router.get('/search-transactions', 
  authenticate, 
  authorize(['admin']), 
  searchTransactions
);

/**
 * @route GET /api/fapshi/service-balance
 * @desc Récupérer le solde du service Fapshi
 * @access Private (Admin only)
 */
router.get('/service-balance', 
  authenticate, 
  authorize(['admin']), 
  getServiceBalance
);

/**
 * @route POST /api/fapshi/webhook
 * @desc Endpoint pour recevoir les webhooks Fapshi
 * @access Public (mais validé par Fapshi)
 */
router.post('/webhook', handleFapshiWebhook);

/**
 * @route POST /api/fapshi/webhook/test
 * @desc Endpoint pour tester les webhooks (développement uniquement)
 * @access Private (Admin only)
 */
router.post('/webhook/test',
  authenticate,
  authorize(['admin']),
  testWebhook
);

/**
 * @route POST /api/fapshi/report-failure
 * @desc Signaler un échec de paiement
 * @access Private
 */
router.post('/report-failure',
  authenticate,
  reportPaymentFailure
);

/**
 * @route POST /api/fapshi/retry-payment/:transId
 * @desc Tenter une reprise de paiement
 * @access Private
 */
router.post('/retry-payment/:transId',
  authenticate,
  retryPayment
);

/**
 * @route GET /api/fapshi/failure-statistics
 * @desc Obtenir les statistiques des échecs de paiement
 * @access Private (Admin)
 */
router.get('/failure-statistics',
  authenticate,
  authorize(['admin']),
  getFailureStatistics
);

module.exports = router;
