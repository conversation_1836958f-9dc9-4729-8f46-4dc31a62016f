const express = require('express');
const router = express.Router();
const { 
  handleWebhook, 
  initiatePayment, 
  checkPaymentStatus,
  searchTransactions,
  getServiceBalance,
  expireTransaction
} = require('../controllers/paymentController');

const { authenticateToken } = require('../middleware/auth');
const { requireRole } = require('../middleware/roleMiddleware');

/**
 * @route POST /api/fapshi/initiate-payment
 * @desc Initier un paiement Fapshi
 * @access Private (Parent, Admin)
 */
router.post('/initiate-payment', 
  authenticateToken, 
  requireRole(['parent', 'admin']), 
  initiatePayment
);

/**
 * @route GET /api/fapshi/payment-status/:transId
 * @desc Vérifier le statut d'un paiement
 * @access Private (Parent, Admin)
 */
router.get('/payment-status/:transId', 
  authenticateToken, 
  requireRole(['parent', 'admin']), 
  checkPaymentStatus
);

/**
 * @route POST /api/fapshi/expire-transaction/:transId
 * @desc Expirer une transaction en attente
 * @access Private (Parent, Admin)
 */
router.post('/expire-transaction/:transId', 
  authenticateToken, 
  requireRole(['parent', 'admin']), 
  expireTransaction
);

/**
 * @route GET /api/fapshi/search-transactions
 * @desc Rechercher des transactions avec filtres
 * @access Private (Admin only)
 */
router.get('/search-transactions', 
  authenticateToken, 
  requireRole(['admin']), 
  searchTransactions
);

/**
 * @route GET /api/fapshi/service-balance
 * @desc Récupérer le solde du service Fapshi
 * @access Private (Admin only)
 */
router.get('/service-balance', 
  authenticateToken, 
  requireRole(['admin']), 
  getServiceBalance
);

/**
 * @route POST /api/fapshi/webhook
 * @desc Endpoint pour recevoir les webhooks Fapshi
 * @access Public (mais validé par Fapshi)
 */
router.post('/webhook', handleWebhook);

module.exports = router;
