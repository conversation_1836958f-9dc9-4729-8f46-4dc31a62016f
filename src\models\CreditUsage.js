const mongoose = require('mongoose');

const CreditUsageSchema = new mongoose.Schema({
  // Référence à l'école
  school_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'School',
    required: true
  },
  
  // Référence à la souscription de l'école
  subscription_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'SchoolSubscription',
    required: true
  },
  
  // Type d'utilisation
  usage_type: {
    type: String,
    enum: ['student_creation', 'chatbot_message', 'manual_deduction', 'refund', 'bonus'],
    required: true
  },
  
  // Nombre de crédits utilisés (peut être négatif pour les remboursements)
  credits_used: {
    type: Number,
    required: true
  },
  
  // Référence à l'entité concernée
  reference_id: {
    type: mongoose.Schema.Types.ObjectId,
    required: false // Peut être null pour certains types d'usage
  },
  
  reference_type: {
    type: String,
    enum: ['Student', 'ChatMessage', 'Manual', 'Other'],
    required: false
  },
  
  // Utilisateur qui a déclenché l'usage
  used_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  // Date d'utilisation
  usage_date: {
    type: Date,
    default: Date.now
  },
  
  // Description de l'utilisation
  description: {
    type: String,
    required: true,
    maxlength: 500
  },
  
  // Détails supplémentaires selon le type
  details: {
    // Pour student_creation
    student_name: String,
    student_id: String,
    class_name: String,
    
    // Pour chatbot_message
    message_content: String,
    response_length: Number,
    conversation_id: String,
    
    // Pour manual_deduction
    admin_reason: String,
    admin_notes: String,
    
    // Métadonnées générales
    metadata: mongoose.Schema.Types.Mixed
  },
  
  // Solde avant et après l'utilisation
  balance_before: {
    type: Number,
    required: true
  },
  
  balance_after: {
    type: Number,
    required: true
  },
  
  // Informations de session
  session_info: {
    ip_address: String,
    user_agent: String,
    device_type: String
  },
  
  // Statut de l'utilisation
  status: {
    type: String,
    enum: ['completed', 'failed', 'reversed'],
    default: 'completed'
  },
  
  // Informations de reversal (annulation)
  reversal_info: {
    reversed_date: Date,
    reversed_by: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    reversal_reason: String
  },
  
  // Notes administratives
  admin_notes: {
    type: String,
    maxlength: 1000
  }
}, {
  timestamps: true
});

// Index pour optimiser les requêtes
CreditUsageSchema.index({ school_id: 1, usage_date: -1 });
CreditUsageSchema.index({ subscription_id: 1, usage_date: -1 });
CreditUsageSchema.index({ usage_type: 1, usage_date: -1 });
CreditUsageSchema.index({ used_by: 1, usage_date: -1 });
CreditUsageSchema.index({ reference_id: 1, reference_type: 1 });

// Méthodes d'instance
CreditUsageSchema.methods.reverse = function(reversed_by, reason) {
  this.status = 'reversed';
  this.reversal_info = {
    reversed_date: new Date(),
    reversed_by: reversed_by,
    reversal_reason: reason
  };
  
  return this.save();
};

// Méthodes statiques
CreditUsageSchema.statics.recordUsage = async function(data) {
  const {
    school_id,
    subscription_id,
    usage_type,
    credits_used,
    reference_id,
    reference_type,
    used_by,
    description,
    details = {},
    balance_before,
    balance_after,
    session_info = {}
  } = data;
  
  const usage = new this({
    school_id,
    subscription_id,
    usage_type,
    credits_used,
    reference_id,
    reference_type,
    used_by,
    description,
    details,
    balance_before,
    balance_after,
    session_info
  });
  
  return usage.save();
};

CreditUsageSchema.statics.getUsageBySchool = function(school_id, options = {}) {
  const {
    limit = 50,
    skip = 0,
    usage_type,
    start_date,
    end_date,
    used_by
  } = options;
  
  const query = { school_id };
  
  if (usage_type) query.usage_type = usage_type;
  if (used_by) query.used_by = used_by;
  if (start_date || end_date) {
    query.usage_date = {};
    if (start_date) query.usage_date.$gte = new Date(start_date);
    if (end_date) query.usage_date.$lte = new Date(end_date);
  }
  
  return this.find(query)
    .populate('used_by', 'name email role')
    .sort({ usage_date: -1 })
    .limit(limit)
    .skip(skip);
};

CreditUsageSchema.statics.getUsageStats = function(school_id, period = 'month') {
  const now = new Date();
  let startDate;
  
  switch (period) {
    case 'day':
      startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      break;
    case 'week':
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case 'month':
      startDate = new Date(now.getFullYear(), now.getMonth(), 1);
      break;
    case 'year':
      startDate = new Date(now.getFullYear(), 0, 1);
      break;
    default:
      startDate = new Date(now.getFullYear(), now.getMonth(), 1);
  }
  
  return this.aggregate([
    {
      $match: {
        school_id: new mongoose.Types.ObjectId(school_id),
        usage_date: { $gte: startDate },
        status: 'completed'
      }
    },
    {
      $group: {
        _id: '$usage_type',
        total_credits: { $sum: '$credits_used' },
        count: { $sum: 1 },
        avg_credits: { $avg: '$credits_used' }
      }
    },
    {
      $sort: { total_credits: -1 }
    }
  ]);
};

CreditUsageSchema.statics.getDailyUsage = function(school_id, days = 30) {
  const endDate = new Date();
  const startDate = new Date(endDate.getTime() - days * 24 * 60 * 60 * 1000);
  
  return this.aggregate([
    {
      $match: {
        school_id: new mongoose.Types.ObjectId(school_id),
        usage_date: { $gte: startDate, $lte: endDate },
        status: 'completed'
      }
    },
    {
      $group: {
        _id: {
          year: { $year: '$usage_date' },
          month: { $month: '$usage_date' },
          day: { $dayOfMonth: '$usage_date' }
        },
        total_credits: { $sum: '$credits_used' },
        count: { $sum: 1 }
      }
    },
    {
      $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 }
    }
  ]);
};

// Middleware pre-save
CreditUsageSchema.pre('save', function(next) {
  // Valider que balance_after = balance_before - credits_used
  if (this.balance_after !== this.balance_before - this.credits_used) {
    return next(new Error('Invalid balance calculation'));
  }
  
  next();
});

const CreditUsage = mongoose.models.CreditUsage || mongoose.model('CreditUsage', CreditUsageSchema);

module.exports = CreditUsage;
