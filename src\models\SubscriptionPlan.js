const mongoose = require('mongoose');

const SubscriptionPlanSchema = new mongoose.Schema({
  plan_name: {
    type: String,
    enum: ['basic', 'standard', 'custom'],
    required: true,
    unique: true
  },
  
  display_name: {
    type: String,
    required: true
  },
  
  description: {
    type: String,
    required: true,
    maxlength: 500
  },
  
  // Tarification
  price_per_credit: {
    type: Number,
    required: true,
    default: 3000 // 3000 FCFA par crédit
  },
  
  minimum_purchase: {
    type: Number,
    required: true,
    default: 1 // Minimum 1 crédit
  },
  
  // Configuration du chatbot
  chatbot_enabled: {
    type: Boolean,
    default: false
  },
  
  chatbot_cost_per_message: {
    type: Number,
    default: 1 // 1 crédit par message
  },
  
  chatbot_monthly_limit: {
    type: Number,
    default: 0 // 0 = pas de limite
  },
  
  // Fonctionnalités incluses
  features: [{
    name: {
      type: String,
      required: true
    },
    description: {
      type: String,
      required: true
    },
    enabled: {
      type: Boolean,
      default: true
    }
  }],
  
  // Avantages du plan
  benefits: [{
    type: String,
    required: true
  }],
  
  // Limitations
  limitations: [{
    type: String
  }],
  
  // Statut du plan
  is_active: {
    type: Boolean,
    default: true
  },
  
  is_popular: {
    type: Boolean,
    default: false
  },
  
  // Ordre d'affichage
  display_order: {
    type: Number,
    default: 0
  },
  
  // Couleur pour l'interface
  color_theme: {
    type: String,
    default: '#3B82F6'
  },
  
  // Contact pour plan custom
  contact_info: {
    email: String,
    phone: String,
    message: String
  }
}, {
  timestamps: true
});

// Index
SubscriptionPlanSchema.index({ plan_name: 1 });
SubscriptionPlanSchema.index({ is_active: 1 });
SubscriptionPlanSchema.index({ display_order: 1 });

// Méthodes statiques
SubscriptionPlanSchema.statics.getActivePlans = function() {
  return this.find({ is_active: true }).sort({ display_order: 1 });
};

SubscriptionPlanSchema.statics.getPlanByName = function(plan_name) {
  return this.findOne({ plan_name, is_active: true });
};

SubscriptionPlanSchema.statics.initializeDefaultPlans = async function() {
  const plans = [
    {
      plan_name: 'basic',
      display_name: 'Plan Basic',
      description: 'Parfait pour commencer avec les fonctionnalités essentielles de gestion scolaire.',
      price_per_credit: 3000,
      minimum_purchase: 1,
      chatbot_enabled: false,
      features: [
        {
          name: 'Gestion des étudiants',
          description: 'Créer et gérer les profils des étudiants',
          enabled: true
        },
        {
          name: 'Gestion des classes',
          description: 'Organiser les classes et les niveaux',
          enabled: true
        },
        {
          name: 'Suivi des présences',
          description: 'Enregistrer et suivre les présences',
          enabled: true
        },
        {
          name: 'Gestion des notes',
          description: 'Saisir et calculer les notes',
          enabled: true
        },
        {
          name: 'Emplois du temps',
          description: 'Créer et gérer les emplois du temps',
          enabled: true
        }
      ],
      benefits: [
        'Gestion complète des étudiants',
        'Suivi des présences et notes',
        'Emplois du temps flexibles',
        'Rapports de base',
        'Support par email'
      ],
      limitations: [
        'Pas d\'accès au chatbot IA',
        'Rapports limités'
      ],
      display_order: 1,
      color_theme: '#10B981'
    },
    {
      plan_name: 'standard',
      display_name: 'Plan Standard',
      description: 'Toutes les fonctionnalités du plan Basic plus l\'accès au chatbot IA pour une assistance avancée.',
      price_per_credit: 3000,
      minimum_purchase: 1,
      chatbot_enabled: true,
      chatbot_cost_per_message: 1,
      features: [
        {
          name: 'Toutes les fonctionnalités Basic',
          description: 'Toutes les fonctionnalités du plan Basic',
          enabled: true
        },
        {
          name: 'Chatbot IA',
          description: 'Assistant IA pour répondre aux questions',
          enabled: true
        },
        {
          name: 'Rapports avancés',
          description: 'Rapports détaillés et analyses',
          enabled: true
        }
      ],
      benefits: [
        'Toutes les fonctionnalités du plan Basic',
        'Chatbot IA intelligent (1 crédit/message)',
        'Rapports avancés et analyses',
        'Support prioritaire',
        'Mises à jour en avant-première'
      ],
      limitations: [],
      is_popular: true,
      display_order: 2,
      color_theme: '#3B82F6'
    },
    {
      plan_name: 'custom',
      display_name: 'Plan Custom',
      description: 'Solution sur mesure avec fonctionnalités personnalisées selon vos besoins spécifiques.',
      price_per_credit: 0, // Tarification personnalisée
      minimum_purchase: 0,
      chatbot_enabled: true,
      features: [
        {
          name: 'Fonctionnalités personnalisées',
          description: 'Développement sur mesure selon vos besoins',
          enabled: true
        },
        {
          name: 'Intégrations spéciales',
          description: 'Intégration avec vos systèmes existants',
          enabled: true
        },
        {
          name: 'Support dédié',
          description: 'Équipe dédiée pour votre projet',
          enabled: true
        }
      ],
      benefits: [
        'Toutes les fonctionnalités Standard',
        'Développement sur mesure',
        'Intégrations personnalisées',
        'Support dédié 24/7',
        'Formation personnalisée',
        'SLA garanti'
      ],
      limitations: [],
      display_order: 3,
      color_theme: '#8B5CF6',
      contact_info: {
        email: '<EMAIL>',
        phone: '+237 XXX XXX XXX',
        message: 'Contactez-nous pour discuter de vos besoins spécifiques et obtenir un devis personnalisé.'
      }
    }
  ];

  for (const planData of plans) {
    await this.findOneAndUpdate(
      { plan_name: planData.plan_name },
      planData,
      { upsert: true, new: true }
    );
  }
};

const SubscriptionPlan = mongoose.models.SubscriptionPlan || mongoose.model('SubscriptionPlan', SubscriptionPlanSchema);

module.exports = SubscriptionPlan;
