import { ENV } from '@/utils/checkEnv';

export interface PaymentFailureData {
  transactionId: string;
  reason: string;
  type: 'network' | 'insufficient_funds' | 'account_issue' | 'validation' | 'limit_exceeded' | 'security' | 'unknown';
  canRetry: boolean;
  retryDelay: number;
  userAction: string;
  severity: 'low' | 'medium' | 'high';
  message: string;
  amount: number;
  credits: number;
  errorCode?: string;
  errorDetails?: any;
}

export interface FailureStatistics {
  totalFailures: number;
  totalLostRevenue: number;
  failuresByReason: Array<{
    _id: string;
    count: number;
    totalAmount: number;
    avgRetries: number;
  }>;
  period: {
    startDate?: string;
    endDate?: string;
  };
}

class PaymentFailureService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = ENV.API_URL;
  }

  /**
   * Signaler un échec de paiement au backend
   */
  async reportPaymentFailure(failureData: PaymentFailureData): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/fapshi/report-failure`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.getAuthToken()}`
        },
        body: JSON.stringify(failureData)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error reporting payment failure:', error);
      throw error;
    }
  }

  /**
   * Tenter une reprise de paiement
   */
  async retryPayment(transactionId: string): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/fapshi/retry-payment/${transactionId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.getAuthToken()}`
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error retrying payment:', error);
      throw error;
    }
  }

  /**
   * Obtenir les statistiques des échecs de paiement
   */
  async getFailureStatistics(filters: {
    startDate?: string;
    endDate?: string;
    schoolId?: string;
  } = {}): Promise<FailureStatistics> {
    try {
      const params = new URLSearchParams();
      if (filters.startDate) params.append('startDate', filters.startDate);
      if (filters.endDate) params.append('endDate', filters.endDate);
      if (filters.schoolId) params.append('schoolId', filters.schoolId);

      const response = await fetch(`${this.baseUrl}/fapshi/failure-statistics?${params}`, {
        headers: {
          'Authorization': `Bearer ${this.getAuthToken()}`
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching failure statistics:', error);
      throw error;
    }
  }

  /**
   * Analyser une raison d'échec et retourner les recommandations
   */
  analyzeFailureReason(reason: string, additionalData: any = {}): PaymentFailureData {
    const reasonLower = reason.toLowerCase();
    
    let analysis: Partial<PaymentFailureData> = {
      reason,
      type: 'unknown',
      canRetry: false,
      retryDelay: 0,
      userAction: 'contact_support',
      severity: 'medium',
      message: reason
    };

    // Échecs liés au réseau ou temporaires
    if (reasonLower.includes('network') || 
        reasonLower.includes('timeout') || 
        reasonLower.includes('temporary') ||
        reasonLower.includes('server error')) {
      analysis = {
        ...analysis,
        type: 'network',
        canRetry: true,
        retryDelay: 5 * 60 * 1000, // 5 minutes
        userAction: 'retry_automatic',
        severity: 'low',
        message: 'Problème de réseau temporaire. Nouvelle tentative automatique prévue.'
      };
    }

    // Échecs liés aux fonds insuffisants
    else if (reasonLower.includes('insufficient') || 
             reasonLower.includes('balance') ||
             reasonLower.includes('funds')) {
      analysis = {
        ...analysis,
        type: 'insufficient_funds',
        canRetry: true,
        retryDelay: 30 * 60 * 1000, // 30 minutes
        userAction: 'check_balance',
        severity: 'medium',
        message: 'Fonds insuffisants. Vérifiez votre solde et réessayez.'
      };
    }

    // Échecs liés à la carte/compte
    else if (reasonLower.includes('card') || 
             reasonLower.includes('account') ||
             reasonLower.includes('blocked') ||
             reasonLower.includes('expired')) {
      analysis = {
        ...analysis,
        type: 'account_issue',
        canRetry: false,
        userAction: 'update_payment_method',
        severity: 'high',
        message: 'Problème avec votre méthode de paiement. Veuillez la mettre à jour.'
      };
    }

    // Échecs liés à la validation
    else if (reasonLower.includes('validation') || 
             reasonLower.includes('invalid') ||
             reasonLower.includes('format')) {
      analysis = {
        ...analysis,
        type: 'validation',
        canRetry: false,
        userAction: 'correct_information',
        severity: 'medium',
        message: 'Informations de paiement invalides. Veuillez les corriger.'
      };
    }

    // Échecs liés aux limites
    else if (reasonLower.includes('limit') || 
             reasonLower.includes('exceeded')) {
      analysis = {
        ...analysis,
        type: 'limit_exceeded',
        canRetry: true,
        retryDelay: 24 * 60 * 60 * 1000, // 24 heures
        userAction: 'wait_or_contact',
        severity: 'medium',
        message: 'Limite de transaction dépassée. Réessayez plus tard ou contactez votre banque.'
      };
    }

    // Échecs de sécurité
    else if (reasonLower.includes('security') || 
             reasonLower.includes('fraud') ||
             reasonLower.includes('suspicious')) {
      analysis = {
        ...analysis,
        type: 'security',
        canRetry: false,
        userAction: 'contact_bank',
        severity: 'high',
        message: 'Transaction bloquée pour des raisons de sécurité. Contactez votre banque.'
      };
    }

    return analysis as PaymentFailureData;
  }

  /**
   * Sauvegarder un échec dans le localStorage pour persistance
   */
  saveFailureToStorage(failure: PaymentFailureData): void {
    try {
      const existingFailures = this.getFailuresFromStorage();
      const updatedFailures = [...existingFailures, failure];
      
      // Garder seulement les 10 derniers échecs
      const recentFailures = updatedFailures.slice(-10);
      
      localStorage.setItem('payment_failures', JSON.stringify(recentFailures));
    } catch (error) {
      console.error('Error saving failure to storage:', error);
    }
  }

  /**
   * Récupérer les échecs du localStorage
   */
  getFailuresFromStorage(): PaymentFailureData[] {
    try {
      const stored = localStorage.getItem('payment_failures');
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Error getting failures from storage:', error);
      return [];
    }
  }

  /**
   * Supprimer un échec du localStorage
   */
  removeFailureFromStorage(transactionId: string): void {
    try {
      const failures = this.getFailuresFromStorage();
      const filtered = failures.filter(f => f.transactionId !== transactionId);
      localStorage.setItem('payment_failures', JSON.stringify(filtered));
    } catch (error) {
      console.error('Error removing failure from storage:', error);
    }
  }

  /**
   * Nettoyer les anciens échecs (plus de 24h)
   */
  cleanupOldFailures(): void {
    try {
      const failures = this.getFailuresFromStorage();
      const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000);
      
      const recentFailures = failures.filter(failure => {
        // Assuming failures have a timestamp field
        const failureTime = new Date(failure.transactionId).getTime();
        return failureTime > oneDayAgo;
      });
      
      localStorage.setItem('payment_failures', JSON.stringify(recentFailures));
    } catch (error) {
      console.error('Error cleaning up old failures:', error);
    }
  }

  /**
   * Créer un rapport d'échec pour l'utilisateur
   */
  generateFailureReport(failures: PaymentFailureData[]): string {
    const report = {
      totalFailures: failures.length,
      failuresByType: failures.reduce((acc, failure) => {
        acc[failure.type] = (acc[failure.type] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
      totalAmount: failures.reduce((sum, failure) => sum + failure.amount, 0),
      mostCommonReason: this.getMostCommonReason(failures),
      timestamp: new Date().toISOString()
    };

    return JSON.stringify(report, null, 2);
  }

  private getMostCommonReason(failures: PaymentFailureData[]): string {
    const reasonCounts = failures.reduce((acc, failure) => {
      acc[failure.type] = (acc[failure.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(reasonCounts)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || 'unknown';
  }

  private getAuthToken(): string {
    // Récupérer le token d'authentification depuis le localStorage ou context
    return localStorage.getItem('auth_token') || '';
  }
}

export default new PaymentFailureService();
