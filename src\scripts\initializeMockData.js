/**
 * Script d'initialisation des données mock pour les tests
 * 
 * Ce script :
 * 1. Initialise les plans de souscription
 * 2. Crée des souscriptions pour les écoles existantes basées sur les données Credit.js
 * 3. Calcule les crédits disponibles basés sur les paiements d'étudiants
 * 
 * Usage: node src/scripts/initializeMockData.js
 */

const mongoose = require('mongoose');
const SubscriptionPlan = require('../models/SubscriptionPlan');
const SchoolSubscription = require('../models/SchoolSubscription');
const Credit = require('../models/Credit');
const School = require('../models/School');
require('dotenv').config();

async function calculateSchoolCredits(schoolId) {
  try {
    // Calculer le total des paiements pour cette école
    const credits = await Credit.aggregate([
      { $match: { school_id: mongoose.Types.ObjectId(schoolId) } },
      { $group: { _id: null, totalPaid: { $sum: "$amountPaid" } } }
    ]);
    
    const totalPaid = credits.length > 0 ? credits[0].totalPaid : 0;
    
    // Convertir en crédits (3000 FCFA = 1 crédit)
    const availableCredits = Math.floor(totalPaid / 3000);
    
    return {
      totalPaid,
      availableCredits,
      creditCount: await Credit.countDocuments({ school_id: schoolId })
    };
  } catch (error) {
    console.error(`Erreur lors du calcul des crédits pour l'école ${schoolId}:`, error);
    return { totalPaid: 0, availableCredits: 0, creditCount: 0 };
  }
}

async function createOrUpdateSchoolSubscription(school) {
  try {
    const schoolCredits = await calculateSchoolCredits(school._id);
    
    // Déterminer le plan basé sur le nombre de crédits
    let planType = 'basic';
    if (schoolCredits.availableCredits >= 50) {
      planType = 'standard';
    }
    if (schoolCredits.availableCredits >= 100) {
      planType = 'custom';
    }
    
    // Créer ou mettre à jour la souscription
    const subscriptionData = {
      school_id: school._id,
      plan_type: planType,
      status: schoolCredits.availableCredits > 0 ? 'active' : 'trial',
      credits_balance: schoolCredits.availableCredits,
      credits_purchased: schoolCredits.availableCredits,
      credits_used: 0,
      features: planType === 'basic' 
        ? ['student_management', 'class_management', 'attendance_tracking', 'grade_management', 'timetable_management']
        : planType === 'standard'
        ? ['student_management', 'class_management', 'attendance_tracking', 'grade_management', 'timetable_management', 'chatbot_access', 'advanced_reports']
        : ['student_management', 'class_management', 'attendance_tracking', 'grade_management', 'timetable_management', 'chatbot_access', 'advanced_reports', 'priority_support', 'custom_features']
    };
    
    const subscription = await SchoolSubscription.findOneAndUpdate(
      { school_id: school._id },
      subscriptionData,
      { upsert: true, new: true }
    );
    
    return {
      school: school.name,
      subscription,
      credits: schoolCredits
    };
  } catch (error) {
    console.error(`Erreur lors de la création de la souscription pour ${school.name}:`, error);
    return null;
  }
}

async function initializeMockData() {
  try {
    console.log('🚀 Initialisation des données mock...\n');
    
    // Connexion à MongoDB
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log('✅ Connexion à MongoDB établie\n');

    // 1. Initialiser les plans de souscription
    console.log('📋 Initialisation des plans de souscription...');
    await SubscriptionPlan.initializeDefaultPlans();
    
    const plans = await SubscriptionPlan.getActivePlans();
    console.log(`✅ ${plans.length} plans initialisés:`);
    plans.forEach(plan => {
      console.log(`   - ${plan.display_name} (${plan.plan_name})`);
    });
    console.log('');

    // 2. Trouver toutes les écoles
    const schools = await School.find({});
    console.log(`🏫 ${schools.length} écoles trouvées\n`);

    // 3. Créer des souscriptions basées sur les données Credit.js
    console.log('💳 Création des souscriptions basées sur les paiements existants...');
    
    let created = 0;
    let updated = 0;
    const results = [];

    for (const school of schools) {
      const result = await createOrUpdateSchoolSubscription(school);
      if (result) {
        results.push(result);
        if (result.subscription.isNew) {
          created++;
        } else {
          updated++;
        }
        
        console.log(`✅ ${result.school}:`);
        console.log(`   Plan: ${result.subscription.plan_type}`);
        console.log(`   Crédits: ${result.credits.availableCredits} (${result.credits.totalPaid} FCFA)`);
        console.log(`   Paiements: ${result.credits.creditCount} étudiants`);
        console.log('');
      }
    }

    // 4. Résumé
    console.log('📊 Résumé de l\'initialisation:');
    console.log(`   Plans créés: ${plans.length}`);
    console.log(`   Souscriptions créées: ${created}`);
    console.log(`   Souscriptions mises à jour: ${updated}`);
    console.log(`   Total écoles traitées: ${results.length}`);
    
    // Statistiques par plan
    const planStats = results.reduce((acc, result) => {
      acc[result.subscription.plan_type] = (acc[result.subscription.plan_type] || 0) + 1;
      return acc;
    }, {});
    
    console.log('\n📈 Répartition par plan:');
    Object.entries(planStats).forEach(([plan, count]) => {
      console.log(`   ${plan}: ${count} écoles`);
    });

    console.log('\n🎉 Initialisation terminée avec succès !');
    
  } catch (error) {
    console.error('❌ Erreur lors de l\'initialisation:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Connexion MongoDB fermée');
  }
}

// Exécuter le script si appelé directement
if (require.main === module) {
  initializeMockData();
}

module.exports = { initializeMockData, calculateSchoolCredits };
