"use client";

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  AlertTriangle, 
  RefreshCw, 
  CreditCard, 
  Clock, 
  Phone, 
  Mail,
  ExternalLink,
  X,
  CheckCircle,
  XCircle
} from 'lucide-react';

interface PaymentFailure {
  transactionId: string;
  reason: string;
  type: 'network' | 'insufficient_funds' | 'account_issue' | 'validation' | 'limit_exceeded' | 'security' | 'unknown';
  canRetry: boolean;
  retryDelay: number;
  userAction: string;
  severity: 'low' | 'medium' | 'high';
  message: string;
  timestamp: string;
  amount: number;
  credits: number;
}

interface PaymentFailureHandlerProps {
  failure: PaymentFailure;
  onRetry: () => Promise<void>;
  onDismiss: () => void;
  onContactSupport: () => void;
  isVisible: boolean;
}

export default function PaymentFailureHandler({
  failure,
  onRetry,
  onDismiss,
  onContactSupport,
  isVisible
}: PaymentFailureHandlerProps) {
  const [isRetrying, setIsRetrying] = useState(false);
  const [retryCountdown, setRetryCountdown] = useState(0);
  const [showDetails, setShowDetails] = useState(false);

  // Countdown pour retry automatique
  useEffect(() => {
    if (failure.canRetry && failure.retryDelay > 0) {
      const endTime = new Date(failure.timestamp).getTime() + failure.retryDelay;
      const interval = setInterval(() => {
        const now = Date.now();
        const remaining = Math.max(0, endTime - now);
        setRetryCountdown(remaining);
        
        if (remaining === 0) {
          clearInterval(interval);
        }
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [failure]);

  const handleRetry = async () => {
    try {
      setIsRetrying(true);
      await onRetry();
    } catch (error) {
      console.error('Retry failed:', error);
    } finally {
      setIsRetrying(false);
    }
  };

  const formatCountdown = (ms: number) => {
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const getFailureIcon = () => {
    switch (failure.type) {
      case 'network':
        return <RefreshCw className="h-6 w-6 text-blue-600" />;
      case 'insufficient_funds':
        return <CreditCard className="h-6 w-6 text-orange-600" />;
      case 'account_issue':
        return <XCircle className="h-6 w-6 text-red-600" />;
      case 'security':
        return <AlertTriangle className="h-6 w-6 text-red-600" />;
      default:
        return <AlertTriangle className="h-6 w-6 text-yellow-600" />;
    }
  };

  const getFailureColors = () => {
    switch (failure.severity) {
      case 'low':
        return {
          bg: 'bg-blue-50 dark:bg-blue-900/20',
          border: 'border-blue-200 dark:border-blue-800',
          text: 'text-blue-800 dark:text-blue-200'
        };
      case 'medium':
        return {
          bg: 'bg-orange-50 dark:bg-orange-900/20',
          border: 'border-orange-200 dark:border-orange-800',
          text: 'text-orange-800 dark:text-orange-200'
        };
      case 'high':
        return {
          bg: 'bg-red-50 dark:bg-red-900/20',
          border: 'border-red-200 dark:border-red-800',
          text: 'text-red-800 dark:text-red-200'
        };
      default:
        return {
          bg: 'bg-gray-50 dark:bg-gray-900/20',
          border: 'border-gray-200 dark:border-gray-800',
          text: 'text-gray-800 dark:text-gray-200'
        };
    }
  };

  const getActionRecommendation = () => {
    switch (failure.userAction) {
      case 'retry_automatic':
        return {
          title: 'Nouvelle tentative automatique',
          description: 'Le système va automatiquement réessayer le paiement.',
          action: null
        };
      case 'check_balance':
        return {
          title: 'Vérifiez votre solde',
          description: 'Assurez-vous d\'avoir suffisamment de fonds sur votre compte.',
          action: 'Vérifier le solde'
        };
      case 'update_payment_method':
        return {
          title: 'Mettre à jour le paiement',
          description: 'Votre méthode de paiement doit être mise à jour.',
          action: 'Changer de méthode'
        };
      case 'contact_bank':
        return {
          title: 'Contactez votre banque',
          description: 'Votre banque a bloqué la transaction pour des raisons de sécurité.',
          action: 'Contacter la banque'
        };
      case 'wait_or_contact':
        return {
          title: 'Attendez ou contactez votre banque',
          description: 'Limite de transaction atteinte. Attendez ou contactez votre banque.',
          action: 'Contacter la banque'
        };
      default:
        return {
          title: 'Contactez le support',
          description: 'Notre équipe peut vous aider à résoudre ce problème.',
          action: 'Contacter le support'
        };
    }
  };

  const colors = getFailureColors();
  const recommendation = getActionRecommendation();

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          className="fixed top-4 right-4 z-50 max-w-md w-full"
        >
          <div className={`${colors.bg} ${colors.border} border rounded-lg shadow-lg overflow-hidden`}>
            {/* Header */}
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-start justify-between">
                <div className="flex items-center">
                  {getFailureIcon()}
                  <div className="ml-3">
                    <h3 className={`text-sm font-medium ${colors.text}`}>
                      Échec de paiement
                    </h3>
                    <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                      Transaction: {failure.transactionId.slice(-8)}
                    </p>
                  </div>
                </div>
                <button
                  onClick={onDismiss}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="p-4">
              <p className={`text-sm ${colors.text} mb-3`}>
                {failure.message}
              </p>

              {/* Transaction details */}
              <div className="bg-white dark:bg-gray-800 rounded-lg p-3 mb-4 text-xs">
                <div className="flex justify-between mb-1">
                  <span className="text-gray-600 dark:text-gray-400">Montant:</span>
                  <span className="font-medium">{failure.amount} XAF</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Crédits:</span>
                  <span className="font-medium">{failure.credits}</span>
                </div>
              </div>

              {/* Recommendation */}
              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                  {recommendation.title}
                </h4>
                <p className="text-xs text-gray-600 dark:text-gray-400 mb-3">
                  {recommendation.description}
                </p>

                {/* Countdown for automatic retry */}
                {failure.canRetry && retryCountdown > 0 && (
                  <div className="flex items-center text-xs text-blue-600 dark:text-blue-400 mb-3">
                    <Clock className="h-3 w-3 mr-1" />
                    <span>Nouvelle tentative dans {formatCountdown(retryCountdown)}</span>
                  </div>
                )}
              </div>

              {/* Actions */}
              <div className="flex flex-col space-y-2">
                {failure.canRetry && retryCountdown === 0 && (
                  <button
                    onClick={handleRetry}
                    disabled={isRetrying}
                    className="flex items-center justify-center px-3 py-2 bg-blue-600 text-white text-xs rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isRetrying ? (
                      <RefreshCw className="h-3 w-3 animate-spin mr-1" />
                    ) : (
                      <RefreshCw className="h-3 w-3 mr-1" />
                    )}
                    Réessayer maintenant
                  </button>
                )}

                {recommendation.action && (
                  <button
                    onClick={onContactSupport}
                    className="flex items-center justify-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 text-xs rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    <Phone className="h-3 w-3 mr-1" />
                    {recommendation.action}
                  </button>
                )}

                <button
                  onClick={() => setShowDetails(!showDetails)}
                  className="text-xs text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                >
                  {showDetails ? 'Masquer' : 'Voir'} les détails
                </button>
              </div>

              {/* Details */}
              <AnimatePresence>
                {showDetails && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700"
                  >
                    <div className="text-xs space-y-2">
                      <div>
                        <span className="text-gray-600 dark:text-gray-400">Type d'échec:</span>
                        <span className="ml-2 font-mono">{failure.type}</span>
                      </div>
                      <div>
                        <span className="text-gray-600 dark:text-gray-400">Sévérité:</span>
                        <span className="ml-2 font-mono">{failure.severity}</span>
                      </div>
                      <div>
                        <span className="text-gray-600 dark:text-gray-400">Heure:</span>
                        <span className="ml-2 font-mono">
                          {new Date(failure.timestamp).toLocaleString()}
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-600 dark:text-gray-400">Raison technique:</span>
                        <span className="ml-2 font-mono text-red-600 dark:text-red-400">
                          {failure.reason}
                        </span>
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

// Hook pour gérer les échecs de paiement
export function usePaymentFailureHandler() {
  const [failures, setFailures] = useState<PaymentFailure[]>([]);

  const addFailure = (failure: Omit<PaymentFailure, 'timestamp'>) => {
    const newFailure: PaymentFailure = {
      ...failure,
      timestamp: new Date().toISOString()
    };
    setFailures(prev => [...prev, newFailure]);
  };

  const removeFailure = (transactionId: string) => {
    setFailures(prev => prev.filter(f => f.transactionId !== transactionId));
  };

  const handleRetry = async (transactionId: string) => {
    // Logique de retry - à implémenter selon vos besoins
    console.log('Retrying payment:', transactionId);
    // Supprimer l'échec après retry réussi
    removeFailure(transactionId);
  };

  const handleContactSupport = () => {
    // Rediriger vers le support ou ouvrir un modal de contact
    window.open('/support', '_blank');
  };

  return {
    failures,
    addFailure,
    removeFailure,
    handleRetry,
    handleContactSupport
  };
}
