const express = require('express');
const router = express.Router();
const {
  getExamPeriods,
  getExamPeriodsByTerm,
  getExamPeriod,
  createExamPeriod,
  updateExamPeriod
} = require('../controllers/examPeriodController');

const { authenticate, authorize } = require('../middleware/middleware');

// Get all exam periods for a school
router.get('/school/:school_id', 
  authenticate, 
  authorize(['admin', 'school_admin', 'teacher']), 
  getExamPeriods
);

// Get exam periods by term
router.get('/school/:school_id/term/:term_id', 
  authenticate, 
  authorize(['admin', 'school_admin', 'teacher']), 
  getExamPeriodsByTerm
);

// Get single exam period
router.get('/school/:school_id/:exam_period_id', 
  authenticate, 
  authorize(['admin', 'school_admin', 'teacher']), 
  getExamPeriod
);

// Create new exam period
router.post('/school/:school_id', 
  authenticate, 
  authorize(['admin', 'school_admin']), 
  createExamPeriod
);

// Update exam period
router.put('/school/:school_id/:exam_period_id', 
  authenticate, 
  authorize(['admin', 'school_admin']), 
  updateExamPeriod
);

// Delete exam period (we'll add this later if needed)
// router.delete('/school/:school_id/:exam_period_id', 
//   authenticate, 
//   authorize(['admin', 'school_admin']), 
//   deleteExamPeriod
// );

module.exports = router;
