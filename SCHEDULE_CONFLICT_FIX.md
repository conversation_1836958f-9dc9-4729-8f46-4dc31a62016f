# Correction des Conflits de Schedule par Type

## Problème Identifié

Dans le système de timetable, lorsqu'on créait un schedule pour l'exam mode, si un schedule existait déjà pour le normal mode dans la même période, un modal de conflit s'affichait. Cependant, cela ne devrait pas être considéré comme un conflit puisque les `schedule_type` sont différents.

## Solution Implémentée

### 1. Modification du Modèle ClassSchedule (`src/models/ClassSchedule.js`)

**Changement dans la méthode `checkConflictWithPriority`:**
- Ajout du filtre `schedule_type` dans la requête de recherche de conflits
- Maintenant, la méthode ne vérifie les conflits que pour les schedules du **même type**
- Simplification de la logique puisque tous les conflits sont maintenant du même type

```javascript
// AVANT
const query = {
  school_id: schoolId,
  class_id: classId,
  period_id: periodId,
  day_of_week: dayOfWeek,
  status: 'active'
};

// APRÈS
const query = {
  school_id: schoolId,
  class_id: classId,
  period_id: periodId,
  day_of_week: dayOfWeek,
  schedule_type: scheduleType, // Nouveau filtre
  status: 'active'
};
```

### 2. Modification du Contrôleur Timetable (`src/controllers/timetableController.js`)

#### A. Vérification des conflits d'enseignants dans `createScheduleEntry`
- Ajout du filtre `schedule_type` pour ne vérifier que les conflits du même type
- Un enseignant peut maintenant avoir un schedule Normal et un schedule Exam au même moment

#### B. Vérification des conflits d'enseignants dans `updateScheduleEntry`
- Même logique appliquée pour la mise à jour des schedules
- Utilisation du `schedule_type` existant ou nouveau pour la vérification

#### C. Vérification des conflits de classe dans `updateScheduleEntry`
- Ajout du filtre `schedule_type` pour les conflits de classe
- Message d'erreur plus précis indiquant le type de schedule en conflit

#### D. Simplification de la logique de gestion des conflits
- Suppression de la distinction entre `different_type` et `same_type` puisque tous les conflits sont maintenant du même type
- Simplification de la logique de remplacement

## Comportement Attendu

### ✅ Cas Autorisés (Pas de Conflit)
1. **Schedule Normal + Schedule Exam** pour la même classe/période/jour
2. **Schedule Normal + Schedule Event** pour la même classe/période/jour
3. **Schedule Exam + Schedule Event** pour la même classe/période/jour
4. **Enseignant avec Schedule Normal + Schedule Exam** au même moment

### ❌ Cas Interdits (Conflit)
1. **Deux Schedules Normal** pour la même classe/période/jour
2. **Deux Schedules Exam** pour la même classe/période/jour
3. **Deux Schedules Event** pour la même classe/période/jour
4. **Enseignant avec deux schedules du même type** au même moment

## Logique Métier

Cette modification respecte la logique métier suivante :
- Les **emplois du temps normaux** ne changent pas pendant les examens
- Les **schedules d'examen** sont créés en parallèle des schedules normaux
- Un enseignant peut superviser un examen tout en ayant un cours normal programmé (bien que dans la pratique, l'un des deux sera suspendu)
- Seuls les schedules du **même type** entrent en conflit direct

## Tests

Un script de test (`test_schedule_conflict.js`) a été créé pour vérifier :
1. Qu'un schedule Normal et un schedule Exam peuvent coexister
2. Qu'un enseignant peut avoir des schedules Normal et Exam au même moment
3. Que deux schedules du même type créent bien un conflit

## Impact

Cette modification permet :
- ✅ Création de schedules d'examen sans conflit avec les schedules normaux
- ✅ Flexibilité dans la gestion des emplois du temps
- ✅ Respect de la logique métier des établissements scolaires
- ✅ Maintien de la vérification des vrais conflits (même type)

## Fichiers Modifiés

1. `src/models/ClassSchedule.js` - Méthode `checkConflictWithPriority`
2. `src/controllers/timetableController.js` - Fonctions `createScheduleEntry` et `updateScheduleEntry`
3. `test_schedule_conflict.js` - Script de test (nouveau)
4. `SCHEDULE_CONFLICT_FIX.md` - Documentation (nouveau)
