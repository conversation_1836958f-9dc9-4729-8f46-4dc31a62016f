"use client";

import React, { createContext, useContext, useEffect, useState } from 'react';
import PaymentNotification from '@/components/notifications/PaymentNotification';
import { checkCreditPurchaseStatus } from '@/app/services/SubscriptionServices';

interface PaymentNotificationContextType {
  showPaymentSuccess: (data: {
    amount: number;
    credits: number;
    transactionId: string;
  }) => void;
  showPaymentFailed: (data: {
    amount?: number;
    credits?: number;
    transactionId?: string;
    reason?: string;
  }) => void;
  showPaymentPending: (data: {
    amount: number;
    credits: number;
    transactionId: string;
  }) => void;
  showPaymentExpired: (data: {
    amount?: number;
    credits?: number;
    transactionId?: string;
  }) => void;
}

const PaymentNotificationContext = createContext<PaymentNotificationContextType | null>(null);

export function usePaymentNotifications() {
  const context = useContext(PaymentNotificationContext);
  if (!context) {
    throw new Error('usePaymentNotifications must be used within PaymentNotificationProvider');
  }
  return context;
}

interface Notification {
  id: string;
  type: 'success' | 'failed' | 'pending' | 'expired';
  title: string;
  message: string;
  amount?: number;
  credits?: number;
  transactionId?: string;
  actionLabel?: string;
  onAction?: () => void;
  autoClose?: boolean;
}

export default function PaymentNotificationProvider({ children }: { children: React.ReactNode }) {
  const [notifications, setNotifications] = useState<Notification[]>([]);

  // Vérifier les paiements en attente au chargement
  useEffect(() => {
    checkPendingPayments();
  }, []);

  const checkPendingPayments = async () => {
    try {
      const pendingPurchase = localStorage.getItem('pending_purchase');
      if (!pendingPurchase) return;

      const data = JSON.parse(pendingPurchase);
      
      // Vérifier si le paiement n'est pas trop ancien (24h max)
      const isRecent = Date.now() - data.timestamp < 24 * 60 * 60 * 1000;
      
      if (isRecent && data.transaction_id) {
        // Vérifier le statut du paiement
        const status = await checkCreditPurchaseStatus(data.transaction_id);
        
        switch (status.payment_status) {
          case 'completed':
            showPaymentSuccess({
              amount: data.total_amount,
              credits: data.credits_amount,
              transactionId: data.transaction_id
            });
            localStorage.removeItem('pending_purchase');
            break;
          case 'failed':
            showPaymentFailed({
              amount: data.total_amount,
              credits: data.credits_amount,
              transactionId: data.transaction_id,
              reason: 'Le paiement a échoué'
            });
            localStorage.removeItem('pending_purchase');
            break;
          case 'expired':
            showPaymentExpired({
              amount: data.total_amount,
              credits: data.credits_amount,
              transactionId: data.transaction_id
            });
            localStorage.removeItem('pending_purchase');
            break;
          case 'pending':
            showPaymentPending({
              amount: data.total_amount,
              credits: data.credits_amount,
              transactionId: data.transaction_id
            });
            break;
        }
      } else {
        // Supprimer les données expirées
        localStorage.removeItem('pending_purchase');
      }
    } catch (error) {
      console.error('Error checking pending payments:', error);
      localStorage.removeItem('pending_purchase');
    }
  };

  const addNotification = (notification: Omit<Notification, 'id'>) => {
    const id = Date.now().toString();
    setNotifications(prev => [...prev, { ...notification, id }]);
  };

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const showPaymentSuccess = (data: {
    amount: number;
    credits: number;
    transactionId: string;
  }) => {
    addNotification({
      type: 'success',
      title: '🎉 Paiement réussi !',
      message: 'Vos crédits ont été ajoutés à votre compte.',
      amount: data.amount,
      credits: data.credits,
      transactionId: data.transactionId,
      autoClose: true
    });
  };

  const showPaymentFailed = (data: {
    amount?: number;
    credits?: number;
    transactionId?: string;
    reason?: string;
  }) => {
    addNotification({
      type: 'failed',
      title: '❌ Paiement échoué',
      message: data.reason || 'Une erreur est survenue lors du paiement.',
      amount: data.amount,
      credits: data.credits,
      transactionId: data.transactionId,
      actionLabel: 'Réessayer',
      onAction: () => window.location.href = '/school-admin/buy-credit',
      autoClose: false
    });
  };

  const showPaymentPending = (data: {
    amount: number;
    credits: number;
    transactionId: string;
  }) => {
    addNotification({
      type: 'pending',
      title: '⏳ Paiement en cours',
      message: 'Votre paiement est en cours de traitement.',
      amount: data.amount,
      credits: data.credits,
      transactionId: data.transactionId,
      autoClose: false
    });
  };

  const showPaymentExpired = (data: {
    amount?: number;
    credits?: number;
    transactionId?: string;
  }) => {
    addNotification({
      type: 'expired',
      title: '⏰ Paiement expiré',
      message: 'Le lien de paiement a expiré après 24 heures.',
      amount: data.amount,
      credits: data.credits,
      transactionId: data.transactionId,
      actionLabel: 'Nouvel achat',
      onAction: () => window.location.href = '/school-admin/buy-credit',
      autoClose: false
    });
  };

  const contextValue: PaymentNotificationContextType = {
    showPaymentSuccess,
    showPaymentFailed,
    showPaymentPending,
    showPaymentExpired
  };

  return (
    <PaymentNotificationContext.Provider value={contextValue}>
      {children}
      
      {/* Render notifications */}
      {notifications.map((notification) => (
        <PaymentNotification
          key={notification.id}
          isVisible={true}
          type={notification.type}
          title={notification.title}
          message={notification.message}
          amount={notification.amount}
          credits={notification.credits}
          transactionId={notification.transactionId}
          actionLabel={notification.actionLabel}
          onAction={notification.onAction}
          onClose={() => removeNotification(notification.id)}
          autoClose={notification.autoClose}
        />
      ))}
    </PaymentNotificationContext.Provider>
  );
}
