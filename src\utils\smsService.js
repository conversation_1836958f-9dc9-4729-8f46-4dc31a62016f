const { Vonage } = require('@vonage/server-sdk');

/**
 * Service SMS utilisant Vonage (Nexmo)
 */
class SMSService {
  constructor() {
    this.vonage = null;
    this.isConfigured = false;
    this.initializeVonage();
  }

  /**
   * Initialiser Vonage avec les clés API
   */
  initializeVonage() {
    try {
      const apiKey = process.env.VONAGE_API_KEY;
      const apiSecret = process.env.VONAGE_API_SECRET;

      if (!apiKey || !apiSecret) {
        console.warn('⚠️ Clés Vonage non configurées. SMS désactivé.');
        return;
      }

      this.vonage = new Vonage({
        apiKey,
        apiSecret
      });

      this.isConfigured = true;
      console.log('✅ Service SMS Vonage initialisé');

    } catch (error) {
      console.error('❌ Erreur initialisation Vonage:', error);
      this.isConfigured = false;
    }
  }

  /**
   * Envoyer un SMS
   * @param {Object} smsData - Données du SMS
   * @param {string} smsData.to - Numéro de téléphone destinataire (format international)
   * @param {string} smsData.message - Message à envoyer
   * @param {string} smsData.from - Numéro expéditeur (optionnel)
   * @returns {Promise<Object>} Résultat de l'envoi
   */
  async sendSMS(smsData) {
    try {
      if (!this.isConfigured) {
        console.warn('⚠️ Service SMS non configuré');
        return { success: false, error: 'SMS service not configured' };
      }

      const { to, message, from = 'Scholarify' } = smsData;

      // Valider le numéro de téléphone
      if (!this.isValidPhoneNumber(to)) {
        throw new Error('Numéro de téléphone invalide');
      }

      // Limiter la longueur du message
      const truncatedMessage = message.length > 160 ? 
        message.substring(0, 157) + '...' : message;

      const result = await this.vonage.sms.send({
        to,
        from,
        text: truncatedMessage
      });

      if (result.messages[0].status === '0') {
        console.log(`📱 SMS envoyé avec succès à ${to}`);
        return {
          success: true,
          messageId: result.messages[0]['message-id'],
          to,
          message: truncatedMessage
        };
      } else {
        throw new Error(result.messages[0]['error-text'] || 'Échec envoi SMS');
      }

    } catch (error) {
      console.error('❌ Erreur envoi SMS:', error);
      return {
        success: false,
        error: error.message,
        to: smsData.to
      };
    }
  }

  /**
   * Envoyer une notification de paiement réussi par SMS
   * @param {Object} data - Données de paiement
   */
  async sendPaymentSuccessSMS(data) {
    const { phone, userName, creditsAmount, totalAmount, transactionId } = data;

    if (!phone) {
      console.warn('⚠️ Numéro de téléphone manquant pour SMS de confirmation');
      return { success: false, error: 'Phone number missing' };
    }

    const message = `✅ Paiement réussi! ${creditsAmount} crédits ajoutés à votre compte Scholarify. Montant: ${totalAmount} XAF. ID: ${transactionId.slice(-6)}`;

    return await this.sendSMS({
      to: phone,
      message
    });
  }

  /**
   * Envoyer une notification d'échec de paiement par SMS
   * @param {Object} data - Données d'échec
   */
  async sendPaymentFailureSMS(data) {
    const { phone, userName, reason, transactionId } = data;

    if (!phone) {
      console.warn('⚠️ Numéro de téléphone manquant pour SMS d\'échec');
      return { success: false, error: 'Phone number missing' };
    }

    const message = `❌ Échec de paiement Scholarify. ${reason}. Réessayez sur votre tableau de bord. ID: ${transactionId?.slice(-6) || 'N/A'}`;

    return await this.sendSMS({
      to: phone,
      message
    });
  }

  /**
   * Envoyer une notification d'expiration par SMS
   * @param {Object} data - Données d'expiration
   */
  async sendPaymentExpirationSMS(data) {
    const { phone, userName, transactionId } = data;

    if (!phone) {
      console.warn('⚠️ Numéro de téléphone manquant pour SMS d\'expiration');
      return { success: false, error: 'Phone number missing' };
    }

    const message = `⏰ Lien de paiement Scholarify expiré après 24h. Aucun débit effectué. Créez un nouvel achat sur votre tableau de bord.`;

    return await this.sendSMS({
      to: phone,
      message
    });
  }

  /**
   * Envoyer une notification de solde faible par SMS
   * @param {Object} data - Données de solde
   */
  async sendLowCreditsSMS(data) {
    const { phone, schoolName, remainingCredits, threshold } = data;

    if (!phone) {
      console.warn('⚠️ Numéro de téléphone manquant pour SMS solde faible');
      return { success: false, error: 'Phone number missing' };
    }

    const message = `⚠️ Solde faible ${schoolName}: ${remainingCredits} crédits restants (seuil: ${threshold}). Rechargez sur Scholarify.`;

    return await this.sendSMS({
      to: phone,
      message
    });
  }

  /**
   * Valider un numéro de téléphone
   * @param {string} phone - Numéro de téléphone
   * @returns {boolean} True si valide
   */
  isValidPhoneNumber(phone) {
    if (!phone || typeof phone !== 'string') {
      return false;
    }

    // Supprimer les espaces et caractères spéciaux
    const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');

    // Vérifier le format international (commence par + suivi de 7-15 chiffres)
    const internationalRegex = /^\+[1-9]\d{6,14}$/;
    
    // Vérifier le format camerounais (237XXXXXXXX ou +237XXXXXXXX)
    const cameroonRegex = /^(\+237|237)?[6-9]\d{8}$/;

    return internationalRegex.test(cleanPhone) || cameroonRegex.test(cleanPhone);
  }

  /**
   * Normaliser un numéro de téléphone camerounais
   * @param {string} phone - Numéro de téléphone
   * @returns {string} Numéro normalisé
   */
  normalizePhoneNumber(phone) {
    if (!phone) return null;

    // Supprimer les espaces et caractères spéciaux
    let cleanPhone = phone.replace(/[\s\-\(\)]/g, '');

    // Si c'est un numéro camerounais sans indicatif
    if (/^[6-9]\d{8}$/.test(cleanPhone)) {
      cleanPhone = '+237' + cleanPhone;
    }
    // Si c'est un numéro camerounais avec 237 mais sans +
    else if (/^237[6-9]\d{8}$/.test(cleanPhone)) {
      cleanPhone = '+' + cleanPhone;
    }
    // Si le numéro commence déjà par +, le garder tel quel
    else if (cleanPhone.startsWith('+')) {
      // Déjà au bon format
    }
    // Autres cas, ajouter + si manquant
    else if (/^\d{10,15}$/.test(cleanPhone)) {
      cleanPhone = '+' + cleanPhone;
    }

    return this.isValidPhoneNumber(cleanPhone) ? cleanPhone : null;
  }

  /**
   * Obtenir le statut du service SMS
   * @returns {Object} Statut du service
   */
  getServiceStatus() {
    return {
      configured: this.isConfigured,
      provider: 'Vonage',
      apiKey: process.env.VONAGE_API_KEY ? 'Configuré' : 'Manquant',
      apiSecret: process.env.VONAGE_API_SECRET ? 'Configuré' : 'Manquant'
    };
  }
}

// Créer une instance singleton
const smsService = new SMSService();

module.exports = smsService;
