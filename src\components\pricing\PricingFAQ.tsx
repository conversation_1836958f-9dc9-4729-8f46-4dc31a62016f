"use client";

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown, ChevronUp, HelpCircle } from 'lucide-react';

interface FAQItem {
  question: string;
  answer: string;
  category: 'general' | 'pricing' | 'features' | 'support';
}

const faqData: FAQItem[] = [
  {
    question: "Comment fonctionne le système de crédits ?",
    answer: "Chaque crédit vous permet de créer un profil d'étudiant dans le système. Pour le plan Standard, les crédits servent aussi pour les messages du chatbot IA (1 crédit = 1 message). Les crédits n'expirent jamais et peuvent être utilisés à votre rythme.",
    category: "general"
  },
  {
    question: "Que se passe-t-il si je n'ai plus de crédits ?",
    answer: "Si votre solde de crédits est épuisé, vous ne pourrez plus créer de nouveaux étudiants ou utiliser le chatbot. Cependant, toutes vos données existantes restent accessibles et vous pouvez continuer à utiliser toutes les autres fonctionnalités. Vous pouvez acheter des crédits supplémentaires à tout moment.",
    category: "general"
  },
  {
    question: "Puis-je changer de plan à tout moment ?",
    answer: "Oui, vous pouvez passer d'un plan à un autre à tout moment. Vos crédits existants sont conservés lors du changement de plan. Si vous passez au plan Standard, vous aurez accès au chatbot IA. Pour le plan Custom, contactez-nous pour une migration personnalisée.",
    category: "pricing"
  },
  {
    question: "Y a-t-il des frais cachés ?",
    answer: "Non, il n'y a aucun frais caché. Vous payez uniquement pour les crédits que vous achetez. Pas de frais d'installation, pas d'abonnement mensuel obligatoire, pas de frais de maintenance. Le prix affiché est le prix final.",
    category: "pricing"
  },
  {
    question: "Combien coûte le chatbot IA ?",
    answer: "Le chatbot IA est inclus dans les plans Standard et Custom. Chaque message que vous envoyez au chatbot coûte 1 crédit. Il n'y a pas de limite mensuelle, vous utilisez vos crédits selon vos besoins.",
    category: "features"
  },
  {
    question: "Puis-je obtenir un remboursement ?",
    answer: "Les crédits achetés ne sont généralement pas remboursables car ils n'expirent jamais. Cependant, en cas de problème technique majeur ou d'insatisfaction dans les 30 premiers jours, contactez notre support pour étudier votre situation.",
    category: "pricing"
  },
  {
    question: "Quelle est la différence entre les plans ?",
    answer: "Le plan Basic inclut toutes les fonctionnalités essentielles de gestion scolaire. Le plan Standard ajoute le chatbot IA et des rapports avancés. Le plan Custom offre des fonctionnalités sur mesure et un support dédié. Tous utilisent le même système de crédits.",
    category: "features"
  },
  {
    question: "Comment puis-je acheter des crédits ?",
    answer: "Vous pouvez acheter des crédits directement depuis votre tableau de bord d'administration. Nous acceptons les paiements par mobile money (Orange Money, MTN Mobile Money) via notre partenaire Fapshi. Le minimum d'achat est de 1 crédit (3000 FCFA).",
    category: "pricing"
  },
  {
    question: "Mes données sont-elles sécurisées ?",
    answer: "Oui, la sécurité de vos données est notre priorité. Nous utilisons un chiffrement SSL, des sauvegardes automatiques quotidiennes, et nos serveurs sont hébergés dans des centres de données sécurisés. Nous respectons les normes de protection des données.",
    category: "general"
  },
  {
    question: "Puis-je importer mes données existantes ?",
    answer: "Oui, nous proposons des outils d'importation pour vos données d'étudiants, classes, et notes existantes. Pour les gros volumes ou des formats spéciaux, notre équipe peut vous assister gratuitement dans la migration.",
    category: "features"
  },
  {
    question: "Quel support est disponible ?",
    answer: "Tous les plans incluent un support par email. Le plan Standard bénéficie d'un support prioritaire avec des temps de réponse plus rapides. Le plan Custom inclut un support dédié avec un contact direct et une assistance téléphonique.",
    category: "support"
  },
  {
    question: "Y a-t-il une période d'essai gratuite ?",
    answer: "Oui ! Chaque nouvelle école reçoit 5 crédits gratuits à l'inscription, vous permettant de créer 5 profils d'étudiants et de tester toutes les fonctionnalités. Aucune carte de crédit requise pour commencer.",
    category: "general"
  }
];

const categories = [
  { key: 'general', label: 'Général', icon: HelpCircle },
  { key: 'pricing', label: 'Tarification', icon: HelpCircle },
  { key: 'features', label: 'Fonctionnalités', icon: HelpCircle },
  { key: 'support', label: 'Support', icon: HelpCircle }
];

export default function PricingFAQ() {
  const [activeCategory, setActiveCategory] = useState<string>('general');
  const [openItems, setOpenItems] = useState<Set<number>>(new Set());

  const toggleItem = (index: number) => {
    const newOpenItems = new Set(openItems);
    if (newOpenItems.has(index)) {
      newOpenItems.delete(index);
    } else {
      newOpenItems.add(index);
    }
    setOpenItems(newOpenItems);
  };

  const filteredFAQ = faqData.filter(item => item.category === activeCategory);

  return (
    <div className="bg-white rounded-2xl shadow-lg p-8">
      {/* Category Tabs */}
      <div className="flex flex-wrap gap-2 mb-8">
        {categories.map(category => (
          <button
            key={category.key}
            onClick={() => setActiveCategory(category.key)}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              activeCategory === category.key
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            <category.icon className="h-4 w-4 inline mr-2" />
            {category.label}
          </button>
        ))}
      </div>

      {/* FAQ Items */}
      <div className="space-y-4">
        {filteredFAQ.map((item, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
            className="border border-gray-200 rounded-lg overflow-hidden"
          >
            <button
              onClick={() => toggleItem(index)}
              className="w-full px-6 py-4 text-left bg-gray-50 hover:bg-gray-100 transition-colors flex justify-between items-center"
            >
              <span className="font-medium text-gray-900 pr-4">
                {item.question}
              </span>
              {openItems.has(index) ? (
                <ChevronUp className="h-5 w-5 text-gray-500 flex-shrink-0" />
              ) : (
                <ChevronDown className="h-5 w-5 text-gray-500 flex-shrink-0" />
              )}
            </button>
            
            <AnimatePresence>
              {openItems.has(index) && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.3 }}
                  className="overflow-hidden"
                >
                  <div className="px-6 py-4 bg-white border-t border-gray-200">
                    <p className="text-gray-700 leading-relaxed">
                      {item.answer}
                    </p>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        ))}
      </div>

      {/* Contact Support */}
      <div className="mt-8 p-6 bg-blue-50 rounded-lg">
        <div className="text-center">
          <h4 className="text-lg font-semibold text-blue-900 mb-2">
            Vous ne trouvez pas la réponse à votre question ?
          </h4>
          <p className="text-blue-700 mb-4">
            Notre équipe support est là pour vous aider
          </p>
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <a
              href="mailto:<EMAIL>"
              className="px-6 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
            >
              Contacter le support
            </a>
            <a
              href="/contact"
              className="px-6 py-2 border-2 border-blue-600 text-blue-600 font-medium rounded-lg hover:bg-blue-600 hover:text-white transition-colors"
            >
              Demander une démo
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
