const SchoolSubscription = require('../models/SchoolSubscription');
const SubscriptionPlan = require('../models/SubscriptionPlan');
const CreditPurchase = require('../models/CreditPurchase');
const CreditUsage = require('../models/CreditUsage');
const School = require('../models/School');
const mongoose = require('mongoose');

// Obtenir la souscription d'une école
const getSchoolSubscription = async (req, res) => {
  try {
    const { school_id } = req.params;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    let subscription = await SchoolSubscription.findOne({ school_id })
      .populate('credit_purchases')
      .populate('school_id', 'name email');

    // Créer une souscription par défaut si elle n'existe pas
    if (!subscription) {
      subscription = await SchoolSubscription.createDefaultSubscription(school_id);
      await subscription.populate('school_id', 'name email');
    }

    // Réinitialiser l'usage mensuel du chatbot si nécessaire
    await subscription.resetMonthlyChatbotUsage();

    res.status(200).json({
      subscription,
      message: 'School subscription retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching school subscription:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Mettre à jour la souscription d'une école
const updateSchoolSubscription = async (req, res) => {
  try {
    const { school_id } = req.params;
    const {
      plan_type,
      low_credit_threshold,
      notifications_enabled,
      admin_notes
    } = req.body;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    const subscription = await SchoolSubscription.findOne({ school_id });
    if (!subscription) {
      return res.status(404).json({ message: 'School subscription not found' });
    }

    // Mettre à jour les champs modifiables
    if (plan_type && ['basic', 'standard', 'custom'].includes(plan_type)) {
      subscription.plan_type = plan_type;
    }
    
    if (typeof low_credit_threshold === 'number') {
      subscription.low_credit_threshold = Math.max(0, low_credit_threshold);
    }
    
    if (typeof notifications_enabled === 'boolean') {
      subscription.notifications_enabled = notifications_enabled;
    }
    
    if (admin_notes !== undefined) {
      subscription.admin_notes = admin_notes;
    }

    await subscription.save();

    res.status(200).json({
      subscription,
      message: 'School subscription updated successfully'
    });
  } catch (error) {
    console.error('Error updating school subscription:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Obtenir les statistiques de la souscription
const getSubscriptionStats = async (req, res) => {
  try {
    const { school_id } = req.params;
    const { period = 'month' } = req.query;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    const subscription = await SchoolSubscription.findOne({ school_id });
    if (!subscription) {
      return res.status(404).json({ message: 'School subscription not found' });
    }

    // Statistiques d'utilisation
    const usageStats = await CreditUsage.getUsageStats(school_id, period);
    
    // Historique des achats récents
    const recentPurchases = await CreditPurchase.getBySchoolId(school_id, 10);
    
    // Utilisation quotidienne
    const dailyUsage = await CreditUsage.getDailyUsage(school_id, 30);

    // Calculs supplémentaires
    const totalPurchased = await CreditPurchase.getTotalPurchasedCredits(school_id);
    const efficiency = {
      credits_per_day: dailyUsage.length > 0 ? 
        dailyUsage.reduce((sum, day) => sum + day.total_credits, 0) / dailyUsage.length : 0,
      burn_rate: subscription.credits_used > 0 && subscription.credits_purchased > 0 ?
        (subscription.credits_used / subscription.credits_purchased) * 100 : 0,
      days_remaining: subscription.credits_balance > 0 && dailyUsage.length > 0 ?
        subscription.credits_balance / (dailyUsage.reduce((sum, day) => sum + day.total_credits, 0) / dailyUsage.length) : 0
    };

    res.status(200).json({
      subscription_overview: {
        plan_type: subscription.plan_type,
        status: subscription.status,
        credits_balance: subscription.credits_balance,
        credits_purchased: subscription.credits_purchased,
        credits_used: subscription.credits_used,
        last_purchase: subscription.last_credit_purchase,
        last_usage: subscription.last_credit_usage
      },
      usage_stats: usageStats,
      recent_purchases: recentPurchases,
      daily_usage: dailyUsage,
      efficiency_metrics: efficiency,
      total_purchased: totalPurchased[0] || { total_credits: 0, total_amount: 0 },
      message: 'Subscription statistics retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching subscription stats:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Vérifier si l'école a suffisamment de crédits
const checkCreditsAvailability = async (req, res) => {
  try {
    const { school_id } = req.params;
    const { credits_needed = 1 } = req.query;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    const subscription = await SchoolSubscription.findOne({ school_id });
    if (!subscription) {
      return res.status(404).json({ message: 'School subscription not found' });
    }

    const hasCredits = subscription.hasCredits(parseInt(credits_needed));
    const isLowBalance = subscription.credits_balance <= subscription.low_credit_threshold;

    res.status(200).json({
      has_credits: hasCredits,
      credits_balance: subscription.credits_balance,
      credits_needed: parseInt(credits_needed),
      is_low_balance: isLowBalance,
      low_credit_threshold: subscription.low_credit_threshold,
      message: hasCredits ? 'Credits available' : 'Insufficient credits'
    });
  } catch (error) {
    console.error('Error checking credits availability:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Déduire des crédits (utilisé par d'autres services)
const deductCredits = async (req, res) => {
  try {
    const { school_id } = req.params;
    const {
      credits_amount = 1,
      usage_type,
      reference_id,
      reference_type,
      description,
      details = {},
      session_info = {}
    } = req.body;

    if (!school_id || !usage_type || !description) {
      return res.status(400).json({ 
        message: 'School ID, usage type, and description are required' 
      });
    }

    const subscription = await SchoolSubscription.findOne({ school_id });
    if (!subscription) {
      return res.status(404).json({ message: 'School subscription not found' });
    }

    if (!subscription.hasCredits(credits_amount)) {
      return res.status(400).json({ 
        message: 'Insufficient credits',
        credits_balance: subscription.credits_balance,
        credits_needed: credits_amount
      });
    }

    const balance_before = subscription.credits_balance;
    
    // Déduire les crédits
    await subscription.deductCredits(credits_amount, usage_type, reference_id, req.user.id);
    
    // Enregistrer l'utilisation
    await CreditUsage.recordUsage({
      school_id,
      subscription_id: subscription._id,
      usage_type,
      credits_used: credits_amount,
      reference_id,
      reference_type,
      used_by: req.user.id,
      description,
      details,
      balance_before,
      balance_after: subscription.credits_balance,
      session_info
    });

    res.status(200).json({
      success: true,
      credits_deducted: credits_amount,
      new_balance: subscription.credits_balance,
      message: 'Credits deducted successfully'
    });
  } catch (error) {
    console.error('Error deducting credits:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Obtenir l'historique d'utilisation des crédits
const getCreditUsageHistory = async (req, res) => {
  try {
    const { school_id } = req.params;
    const {
      limit = 50,
      skip = 0,
      usage_type,
      start_date,
      end_date,
      used_by
    } = req.query;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    const options = {
      limit: parseInt(limit),
      skip: parseInt(skip),
      usage_type,
      start_date,
      end_date,
      used_by
    };

    const usageHistory = await CreditUsage.getUsageBySchool(school_id, options);
    const totalCount = await CreditUsage.countDocuments({ school_id });

    res.status(200).json({
      usage_history: usageHistory,
      pagination: {
        total: totalCount,
        limit: parseInt(limit),
        skip: parseInt(skip),
        has_more: (parseInt(skip) + parseInt(limit)) < totalCount
      },
      message: 'Credit usage history retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching credit usage history:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Obtenir tous les plans de souscription disponibles
const getAvailablePlans = async (req, res) => {
  try {
    const plans = await SubscriptionPlan.getActivePlans();

    res.status(200).json({
      plans,
      message: 'Available subscription plans retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching available plans:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

module.exports = {
  getSchoolSubscription,
  updateSchoolSubscription,
  getSubscriptionStats,
  checkCreditsAvailability,
  deductCredits,
  getCreditUsageHistory,
  getAvailablePlans
};
