# ActivityLog & Notification Test Instructions

## Test ActivityLog via API Endpoint

1. **Start the server** (if not already running):
   ```bash
   npm start
   ```

2. **Get authentication token** by logging in as school admin

3. **Test ActivityLog endpoint**:
   ```bash
   curl -X POST http://localhost:5000/api/timetable/test-activity-log \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN_HERE" \
     -d '{"school_id": "YOUR_SCHOOL_ID"}'
   ```

## Test Notifications via API Endpoint

4. **Test Notification endpoint**:
   ```bash
   curl -X POST http://localhost:5000/api/timetable/test-notification \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN_HERE" \
     -d '{"teacher_id": "TEACHER_ID", "school_id": "YOUR_SCHOOL_ID"}'
   ```

5. **Check notifications in frontend**:
   - <PERSON>gin as the teacher specified in the test
   - Check the notification bell in the dashboard
   - Verify the notification appears in the notification center

## Expected Responses

### ActivityLog Response

If ActivityLog is working correctly:
```json
{
  "success": true,
  "log_created": {
    "id": "...",
    "log_id": "LOG_...",
    "action": "schedule_created",
    "created_at": "..."
  },
  "total_logs": 1,
  "recent_logs_count": 1,
  "message": "ActivityLog test successful"
}
```

If ActivityLog is failing:
```json
{
  "success": false,
  "log_created": null,
  "total_logs": 0,
  "recent_logs_count": 0,
  "message": "ActivityLog test failed"
}

### Notification Response

If Notifications are working correctly:
```json
{
  "success": true,
  "notification_created": {
    "id": "...",
    "notification_id": "NOT_...",
    "title": "Exam Supervision Assignment 📝",
    "category": "schedule",
    "recipient_id": "...",
    "created_at": "..."
  },
  "user_notifications_count": 1,
  "recent_notifications": [
    {
      "id": "NOT_...",
      "title": "Exam Supervision Assignment 📝",
      "category": "schedule",
      "read": false,
      "created_at": "..."
    }
  ],
  "message": "Notification test successful"
}
```

If Notifications are failing:
```json
{
  "success": false,
  "notification_created": null,
  "user_notifications_count": 0,
  "recent_notifications": [],
  "message": "Notification test failed"
}
```

## Troubleshooting

### Common Issues:

1. **Missing generateId utility**: Fixed by creating `src/utils/generateId.js`
2. **MongoDB connection issues**: Check connection string and database permissions
3. **Schema validation errors**: Check if all required fields are provided
4. **Unique constraint violations**: The generateId utility should handle this

### Check Server Logs

Look for these messages in the server console:
- `🧪 Testing ActivityLog functionality...`
- `✅ ActivityLog test result: SUCCESS/FAILED`
- `📝 Created log: {...}`
- `📊 Total ActivityLogs in database: X`

### Manual Database Check

Connect to MongoDB and check:
```javascript
// In MongoDB shell
use scholarify
db.activitylogs.find().limit(5).sort({createdAt: -1})
db.activitylogs.countDocuments()
```

## Fix Applied

✅ **Created missing `src/utils/generateId.js`** - This was the main issue
✅ **Added test endpoint** - `/api/timetable/test-activity-log`
✅ **Enhanced error handling** - Better error messages and logging

The ActivityLog should now work correctly for all timetable operations including exam supervision assignments.
