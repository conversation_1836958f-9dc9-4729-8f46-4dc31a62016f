const ExamPeriod = require('../models/ExamPeriod');
const Term = require('../models/Term');
const ClassSchedule = require('../models/ClassSchedule');
const ActivityLog = require('../models/ActivityLog');

// Get all exam periods for a school
const getExamPeriods = async (req, res) => {
  try {
    const { school_id } = req.params;
    const { term_id, academic_year, status } = req.query;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    // Build query
    const query = { school_id };
    if (term_id) query.term_id = term_id;
    if (academic_year) query.academic_year = academic_year;
    if (status) query.status = status;

    const examPeriods = await ExamPeriod.find(query)
      .populate('term_id', 'name term_type start_date end_date')
      .populate('created_by', 'first_name last_name name')
      .populate('updated_by', 'first_name last_name name')
      .sort({ start_date: 1 });

    res.status(200).json({
      exam_periods: examPeriods,
      total: examPeriods.length,
      message: 'Exam periods retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching exam periods:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Get exam periods by term
const getExamPeriodsByTerm = async (req, res) => {
  try {
    const { school_id, term_id } = req.params;

    if (!school_id || !term_id) {
      return res.status(400).json({ message: 'School ID and Term ID are required' });
    }

    const examPeriods = await ExamPeriod.getByTerm(school_id, term_id);

    res.status(200).json({
      exam_periods: examPeriods,
      total: examPeriods.length,
      message: 'Exam periods for term retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching exam periods by term:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Get single exam period
const getExamPeriod = async (req, res) => {
  try {
    const { school_id, exam_period_id } = req.params;

    const examPeriod = await ExamPeriod.findOne({
      school_id,
      _id: exam_period_id
    })
      .populate('term_id', 'name term_type start_date end_date')
      .populate('created_by', 'first_name last_name name')
      .populate('updated_by', 'first_name last_name name');

    if (!examPeriod) {
      return res.status(404).json({ message: 'Exam period not found' });
    }

    // Get associated exam schedules
    const examSchedules = await ClassSchedule.getByExamPeriod(exam_period_id);

    res.status(200).json({
      exam_period: examPeriod,
      exam_schedules: examSchedules,
      message: 'Exam period retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching exam period:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Create new exam period
const createExamPeriod = async (req, res) => {
  try {
    const { school_id } = req.params;
    const {
      term_id,
      name,
      description,
      start_date,
      end_date,
      exam_type,
      academic_year,
      session_year,
      priority,
      settings,
      notes
    } = req.body;

    if (!school_id || !term_id || !name || !start_date || !end_date || !academic_year || !session_year) {
      return res.status(400).json({
        message: 'School ID, term ID, name, start date, end date, academic year, and session year are required'
      });
    }

    // Validate dates
    if (new Date(end_date) <= new Date(start_date)) {
      return res.status(400).json({
        message: 'End date must be after start date'
      });
    }

    // Check for conflicts with existing exam periods
    const conflicts = await ExamPeriod.checkConflicts(school_id, new Date(start_date), new Date(end_date));
    if (conflicts.length > 0) {
      return res.status(400).json({
        message: 'Exam period conflicts with existing exam periods',
        conflicts: conflicts.map(conflict => ({
          name: conflict.name,
          start_date: conflict.start_date,
          end_date: conflict.end_date,
          term: conflict.term_id?.name
        }))
      });
    }

    // Verify term exists
    const term = await Term.findOne({ _id: term_id, school_id });
    if (!term) {
      return res.status(404).json({ message: 'Term not found' });
    }
    const exam_period_id = "EP" + Date.now().toString(36).toUpperCase();
    const examPeriod = new ExamPeriod({
      exam_period_id,
      school_id,
      term_id,
      name,
      description,
      start_date: new Date(start_date),
      end_date: new Date(end_date),
      exam_type: exam_type || 'test',
      academic_year,
      session_year,
      priority: priority || 100,
      settings: settings || {},
      notes,
      created_by: req.user._id
    });

    await examPeriod.save();

    // Log activity
    try {
      await ActivityLog.logActivity({
        user_id: req.user._id,
        school_id,
        action: 'exam_period_created',
        entity_type: 'exam_period',
        entity_id: examPeriod._id,
        details: {
          exam_period_name: name,
          term_name: term.name,
          start_date,
          end_date,
          exam_type
        }
      });
    } catch (logError) {
      console.error('Error logging activity:', logError);
    }

    const populatedExamPeriod = await ExamPeriod.findById(examPeriod._id)
      .populate('term_id', 'name term_type start_date end_date')
      .populate('created_by', 'first_name last_name name');

    res.status(201).json({
      exam_period: populatedExamPeriod,
      message: 'Exam period created successfully'
    });
  } catch (error) {
    console.error('Error creating exam period:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Update exam period
const updateExamPeriod = async (req, res) => {
  try {
    const { school_id, exam_period_id } = req.params;
    const updateData = req.body;

    const examPeriod = await ExamPeriod.findOne({
      school_id,
      _id: exam_period_id
    });

    if (!examPeriod) {
      return res.status(404).json({ message: 'Exam period not found' });
    }

    // Validate dates if they're being updated
    if (updateData.start_date || updateData.end_date) {
      const startDate = new Date(updateData.start_date || examPeriod.start_date);
      const endDate = new Date(updateData.end_date || examPeriod.end_date);

      if (endDate <= startDate) {
        return res.status(400).json({
          message: 'End date must be after start date'
        });
      }

      // Check for conflicts (excluding current exam period)
      const conflicts = await ExamPeriod.checkConflicts(school_id, startDate, endDate, exam_period_id);
      if (conflicts.length > 0) {
        return res.status(400).json({
          message: 'Updated exam period would conflict with existing exam periods',
          conflicts: conflicts.map(conflict => ({
            name: conflict.name,
            start_date: conflict.start_date,
            end_date: conflict.end_date,
            term: conflict.term_id?.name
          }))
        });
      }
    }

    // Update fields
    Object.keys(updateData).forEach(key => {
      if (key !== '_id' && key !== 'exam_period_id' && key !== 'school_id') {
        examPeriod[key] = updateData[key];
      }
    });

    examPeriod.updated_by = req.user._id;
    await examPeriod.save();

    // Log activity
    try {
      await ActivityLog.logActivity({
        user_id: req.user._id,
        school_id,
        action: 'exam_period_updated',
        entity_type: 'exam_period',
        entity_id: examPeriod._id,
        details: {
          exam_period_name: examPeriod.name,
          updated_fields: Object.keys(updateData)
        }
      });
    } catch (logError) {
      console.error('Error logging activity:', logError);
    }

    const updatedExamPeriod = await ExamPeriod.findById(examPeriod._id)
      .populate('term_id', 'name term_type start_date end_date')
      .populate('created_by', 'first_name last_name name')
      .populate('updated_by', 'first_name last_name name');

    res.status(200).json({
      exam_period: updatedExamPeriod,
      message: 'Exam period updated successfully'
    });
  } catch (error) {
    console.error('Error updating exam period:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

module.exports = {
  getExamPeriods,
  getExamPeriodsByTerm,
  getExamPeriod,
  createExamPeriod,
  updateExamPeriod
};
