import { useState, useEffect, useCallback } from 'react';
import { 
  initiateCreditPurchase, 
  checkCreditPurchaseStatus 
} from '@/app/services/SubscriptionServices';

interface PendingPurchase {
  transaction_id: string;
  purchase_id: string;
  credits_amount: number;
  total_amount: number;
  timestamp: number;
}

interface PaymentState {
  status: 'idle' | 'initiating' | 'redirecting' | 'checking' | 'success' | 'failed' | 'expired' | 'pending';
  error: string | null;
  purchaseData: any | null;
  loading: boolean;
}

interface UseFapshiPaymentOptions {
  onSuccess?: (data: any) => void;
  onError?: (error: string) => void;
  onStatusChange?: (status: PaymentState['status']) => void;
  autoCheckPending?: boolean;
  maxRetries?: number;
  retryInterval?: number;
}

export function useFapshiPayment(options: UseFapshiPaymentOptions = {}) {
  const {
    onSuccess,
    onError,
    onStatusChange,
    autoCheckPending = true,
    maxRetries = 10,
    retryInterval = 3000
  } = options;

  const [state, setState] = useState<PaymentState>({
    status: 'idle',
    error: null,
    purchaseData: null,
    loading: false
  });

  const [retryCount, setRetryCount] = useState(0);

  // Mettre à jour le statut et notifier
  const updateStatus = useCallback((newStatus: PaymentState['status'], data?: Partial<PaymentState>) => {
    setState(prev => ({
      ...prev,
      status: newStatus,
      ...data
    }));
    onStatusChange?.(newStatus);
  }, [onStatusChange]);

  // Initier un paiement Fapshi
  const initiatePayment = useCallback(async (paymentData: {
    school_id: string;
    credits_amount: number;
    billing_info: any;
    redirect_url?: string;
  }) => {
    try {
      updateStatus('initiating', { loading: true, error: null });

      const purchaseData = {
        ...paymentData,
        payment_method: 'fapshi',
        redirect_url: paymentData.redirect_url || `${window.location.origin}/school-admin/buy-credit/success`
      };

      const response = await initiateCreditPurchase(purchaseData);

      // Vérifier la réponse de Fapshi
      if (response.payment_response?.statusCode === 200) {
        const paymentUrl = response.payment_response.link;
        
        if (paymentUrl) {
          // Sauvegarder les informations de transaction
          const pendingPurchase: PendingPurchase = {
            transaction_id: response.purchase.transaction_id,
            purchase_id: response.purchase.purchase_id,
            credits_amount: paymentData.credits_amount,
            total_amount: response.purchase.total_amount,
            timestamp: Date.now()
          };

          localStorage.setItem('pending_purchase', JSON.stringify(pendingPurchase));
          
          updateStatus('redirecting', { 
            loading: false, 
            purchaseData: response.purchase 
          });

          // Rediriger vers Fapshi
          window.location.href = paymentUrl;
        } else {
          throw new Error('Lien de paiement non reçu de Fapshi');
        }
      } else {
        const errorMessage = response.payment_response?.message || 'Erreur lors de l\'initiation du paiement';
        throw new Error(errorMessage);
      }
    } catch (error: any) {
      console.error('Payment initiation error:', error);
      const errorMessage = error.message || 'Erreur lors de l\'initiation du paiement';
      updateStatus('failed', { 
        loading: false, 
        error: errorMessage 
      });
      onError?.(errorMessage);
    }
  }, [updateStatus, onError]);

  // Vérifier le statut d'un paiement
  const checkPaymentStatus = useCallback(async (transactionId: string) => {
    try {
      updateStatus('checking', { loading: true, error: null });

      const result = await checkCreditPurchaseStatus(transactionId);

      switch (result.payment_status) {
        case 'completed':
          updateStatus('success', { 
            loading: false, 
            purchaseData: result 
          });
          localStorage.removeItem('pending_purchase');
          onSuccess?.(result);
          break;

        case 'failed':
          updateStatus('failed', { 
            loading: false, 
            error: 'Le paiement a échoué',
            purchaseData: result 
          });
          localStorage.removeItem('pending_purchase');
          onError?.('Le paiement a échoué');
          break;

        case 'expired':
          updateStatus('expired', { 
            loading: false, 
            error: 'Le lien de paiement a expiré',
            purchaseData: result 
          });
          localStorage.removeItem('pending_purchase');
          onError?.('Le lien de paiement a expiré');
          break;

        case 'pending':
          updateStatus('pending', { 
            loading: false, 
            purchaseData: result 
          });
          
          // Réessayer automatiquement si activé
          if (autoCheckPending && retryCount < maxRetries) {
            setTimeout(() => {
              setRetryCount(prev => prev + 1);
              checkPaymentStatus(transactionId);
            }, retryInterval);
          }
          break;

        default:
          updateStatus('pending', { 
            loading: false, 
            purchaseData: result 
          });
      }

      return result;
    } catch (error: any) {
      console.error('Error checking payment status:', error);
      const errorMessage = error.message || 'Erreur lors de la vérification du paiement';
      updateStatus('failed', { 
        loading: false, 
        error: errorMessage 
      });
      onError?.(errorMessage);
    }
  }, [updateStatus, onSuccess, onError, autoCheckPending, retryCount, maxRetries, retryInterval]);

  // Vérifier les paiements en attente au chargement
  const checkPendingPayments = useCallback(async () => {
    try {
      const pendingPurchase = localStorage.getItem('pending_purchase');
      if (!pendingPurchase) return;

      const data: PendingPurchase = JSON.parse(pendingPurchase);
      
      // Vérifier si le paiement n'est pas trop ancien (24h max)
      const isRecent = Date.now() - data.timestamp < 24 * 60 * 60 * 1000;
      
      if (isRecent && data.transaction_id) {
        await checkPaymentStatus(data.transaction_id);
      } else {
        // Supprimer les données expirées
        localStorage.removeItem('pending_purchase');
      }
    } catch (error) {
      console.error('Error checking pending payments:', error);
      localStorage.removeItem('pending_purchase');
    }
  }, [checkPaymentStatus]);

  // Réinitialiser l'état
  const reset = useCallback(() => {
    setState({
      status: 'idle',
      error: null,
      purchaseData: null,
      loading: false
    });
    setRetryCount(0);
  }, []);

  // Réessayer la vérification du statut
  const retry = useCallback(() => {
    if (state.purchaseData?.transaction_id) {
      setRetryCount(0);
      checkPaymentStatus(state.purchaseData.transaction_id);
    }
  }, [state.purchaseData, checkPaymentStatus]);

  // Vérifier les paiements en attente au montage du composant
  useEffect(() => {
    checkPendingPayments();
  }, [checkPendingPayments]);

  return {
    // État
    status: state.status,
    error: state.error,
    purchaseData: state.purchaseData,
    loading: state.loading,
    retryCount,

    // Actions
    initiatePayment,
    checkPaymentStatus,
    checkPendingPayments,
    retry,
    reset,

    // Helpers
    isIdle: state.status === 'idle',
    isInitiating: state.status === 'initiating',
    isRedirecting: state.status === 'redirecting',
    isChecking: state.status === 'checking',
    isSuccess: state.status === 'success',
    isFailed: state.status === 'failed',
    isExpired: state.status === 'expired',
    isPending: state.status === 'pending',
    canRetry: state.status === 'pending' && retryCount < maxRetries,
    hasError: !!state.error
  };
}

// Hook simplifié pour les cas d'usage basiques
export function useSimpleFapshiPayment() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const initiatePayment = async (paymentData: {
    school_id: string;
    credits_amount: number;
    billing_info: any;
  }) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await initiateCreditPurchase({
        ...paymentData,
        payment_method: 'fapshi',
        redirect_url: `${window.location.origin}/school-admin/buy-credit/success`
      });

      if (response.payment_response?.statusCode === 200 && response.payment_response.link) {
        // Sauvegarder et rediriger
        localStorage.setItem('pending_purchase', JSON.stringify({
          transaction_id: response.purchase.transaction_id,
          purchase_id: response.purchase.purchase_id,
          credits_amount: paymentData.credits_amount,
          total_amount: response.purchase.total_amount,
          timestamp: Date.now()
        }));

        window.location.href = response.payment_response.link;
      } else {
        throw new Error(response.payment_response?.message || 'Erreur lors de l\'initiation du paiement');
      }
    } catch (err: any) {
      setError(err.message || 'Erreur lors de l\'initiation du paiement');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    initiatePayment,
    isLoading,
    error,
    clearError: () => setError(null)
  };
}
