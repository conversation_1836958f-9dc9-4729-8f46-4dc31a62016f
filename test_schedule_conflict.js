/**
 * Test script pour vérifier que les conflits de schedule prennent en compte le schedule_type
 * 
 * Ce script teste que:
 * 1. Un schedule Normal et un schedule Exam peuvent coexister pour la même classe/période/jour
 * 2. Deux schedules du même type pour la même classe/période/jour créent un conflit
 * 3. Les enseignants peuvent avoir des schedules Normal et Exam au même moment
 */

const mongoose = require('mongoose');
const ClassSchedule = require('./src/models/ClassSchedule');

// Configuration de test
const testConfig = {
  school_id: new mongoose.Types.ObjectId(),
  class_id: new mongoose.Types.ObjectId(),
  subject_id_1: new mongoose.Types.ObjectId(),
  subject_id_2: new mongoose.Types.ObjectId(),
  teacher_id: new mongoose.Types.ObjectId(),
  period_id: new mongoose.Types.ObjectId(),
  day_of_week: 'Monday',
  academic_year: '2024-2025',
  session_year: '2024'
};

async function runTests() {
  try {
    console.log('🧪 Démarrage des tests de conflit de schedule...\n');

    // Test 1: Créer un schedule Normal
    console.log('📝 Test 1: Création d\'un schedule Normal');
    const normalSchedule = new ClassSchedule({
      school_id: testConfig.school_id,
      class_id: testConfig.class_id,
      subject_id: testConfig.subject_id_1,
      teacher_id: testConfig.teacher_id,
      period_id: testConfig.period_id,
      day_of_week: testConfig.day_of_week,
      schedule_type: 'Normal',
      academic_year: testConfig.academic_year,
      session_year: testConfig.session_year
    });

    // Test 2: Vérifier qu'un schedule Exam peut être créé au même moment
    console.log('📝 Test 2: Vérification qu\'un schedule Exam peut coexister');
    const examConflictCheck = await ClassSchedule.checkConflictWithPriority(
      testConfig.school_id,
      testConfig.class_id,
      testConfig.period_id,
      testConfig.day_of_week,
      'Exam'
    );

    console.log('Résultat du conflit Exam vs Normal:', {
      hasConflict: examConflictCheck.hasConflict,
      conflictType: examConflictCheck.conflictType,
      canReplace: examConflictCheck.canReplace
    });

    if (!examConflictCheck.hasConflict) {
      console.log('✅ Test 2 RÉUSSI: Aucun conflit détecté entre schedule Normal et Exam');
    } else {
      console.log('❌ Test 2 ÉCHOUÉ: Conflit détecté alors qu\'il ne devrait pas y en avoir');
    }

    // Test 3: Créer un schedule Exam
    console.log('\n📝 Test 3: Création d\'un schedule Exam');
    const examSchedule = new ClassSchedule({
      school_id: testConfig.school_id,
      class_id: testConfig.class_id,
      subject_id: testConfig.subject_id_2,
      teacher_id: testConfig.teacher_id,
      period_id: testConfig.period_id,
      day_of_week: testConfig.day_of_week,
      schedule_type: 'Exam',
      exam_period_id: new mongoose.Types.ObjectId(),
      academic_year: testConfig.academic_year,
      session_year: testConfig.session_year
    });

    // Test 4: Vérifier qu'un autre schedule Normal peut être créé au même moment
    console.log('📝 Test 4: Vérification qu\'un autre schedule Normal peut coexister avec Exam');
    const normalConflictCheck = await ClassSchedule.checkConflictWithPriority(
      testConfig.school_id,
      testConfig.class_id,
      testConfig.period_id,
      testConfig.day_of_week,
      'Normal'
    );

    console.log('Résultat du conflit Normal vs Exam:', {
      hasConflict: normalConflictCheck.hasConflict,
      conflictType: normalConflictCheck.conflictType,
      canReplace: normalConflictCheck.canReplace
    });

    if (!normalConflictCheck.hasConflict) {
      console.log('✅ Test 4 RÉUSSI: Aucun conflit détecté entre schedule Exam et Normal');
    } else {
      console.log('❌ Test 4 ÉCHOUÉ: Conflit détecté alors qu\'il ne devrait pas y en avoir');
    }

    // Test 5: Vérifier qu'un autre schedule du même type crée un conflit
    console.log('\n📝 Test 5: Vérification qu\'un autre schedule Normal crée un conflit');
    const sameTypeConflictCheck = await ClassSchedule.checkConflictWithPriority(
      testConfig.school_id,
      testConfig.class_id,
      testConfig.period_id,
      testConfig.day_of_week,
      'Normal'
    );

    console.log('Résultat du conflit Normal vs Normal:', {
      hasConflict: sameTypeConflictCheck.hasConflict,
      conflictType: sameTypeConflictCheck.conflictType,
      canReplace: sameTypeConflictCheck.canReplace
    });

    if (sameTypeConflictCheck.hasConflict && sameTypeConflictCheck.conflictType === 'same_type') {
      console.log('✅ Test 5 RÉUSSI: Conflit détecté entre schedules du même type');
    } else {
      console.log('❌ Test 5 ÉCHOUÉ: Aucun conflit détecté alors qu\'il devrait y en avoir un');
    }

    console.log('\n🎉 Tests terminés!');

  } catch (error) {
    console.error('❌ Erreur lors des tests:', error);
  }
}

// Fonction pour nettoyer les données de test
async function cleanup() {
  try {
    await ClassSchedule.deleteMany({
      school_id: testConfig.school_id
    });
    console.log('🧹 Nettoyage des données de test terminé');
  } catch (error) {
    console.error('❌ Erreur lors du nettoyage:', error);
  }
}

// Exporter les fonctions pour utilisation
module.exports = {
  runTests,
  cleanup,
  testConfig
};

// Si le script est exécuté directement
if (require.main === module) {
  console.log('⚠️  Ce script nécessite une connexion à MongoDB pour fonctionner.');
  console.log('💡 Utilisez-le dans un environnement où MongoDB est configuré.');
  console.log('📖 Exemple d\'utilisation:');
  console.log('   const { runTests, cleanup } = require("./test_schedule_conflict");');
  console.log('   await runTests();');
  console.log('   await cleanup();');
}
