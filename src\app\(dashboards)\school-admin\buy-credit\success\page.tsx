"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  CheckCircle, 
  CreditCard, 
  ArrowRight, 
  Loader2,
  AlertCircle,
  RefreshCw
} from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { checkCreditPurchaseStatus } from '@/app/services/SubscriptionServices';
import { useCreditContext } from '@/context/CreditContext';

export default function CreditPurchaseSuccessPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { refreshAll } = useCreditContext();
  
  const [paymentStatus, setPaymentStatus] = useState<'checking' | 'success' | 'failed' | 'pending'>('checking');
  const [purchaseDetails, setPurchaseDetails] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  const transaction_id = searchParams.get('transaction_id') || searchParams.get('transId');
  const purchase_id = searchParams.get('purchase_id') || searchParams.get('externalId');

  useEffect(() => {
    if (transaction_id) {
      checkPaymentStatus();
    } else {
      setError('ID de transaction manquant');
      setPaymentStatus('failed');
    }
  }, [transaction_id]);

  const checkPaymentStatus = async () => {
    if (!transaction_id) return;

    try {
      setPaymentStatus('checking');
      setError(null);

      const response = await checkCreditPurchaseStatus(transaction_id);
      setPurchaseDetails(response);

      if (response.payment_status === 'completed') {
        setPaymentStatus('success');
        // Refresh credit context to show updated balance
        await refreshAll();
      } else if (response.payment_status === 'failed') {
        setPaymentStatus('failed');
        setError('Le paiement a échoué');
      } else {
        setPaymentStatus('pending');
        // Retry after 3 seconds if still pending and retry count < 10
        if (retryCount < 10) {
          setTimeout(() => {
            setRetryCount(prev => prev + 1);
            checkPaymentStatus();
          }, 3000);
        } else {
          setError('Le paiement prend plus de temps que prévu. Veuillez vérifier votre tableau de bord.');
        }
      }
    } catch (err: any) {
      console.error('Error checking payment status:', err);
      setError(err.message || 'Erreur lors de la vérification du paiement');
      setPaymentStatus('failed');
    }
  };

  const handleRetry = () => {
    setRetryCount(0);
    checkPaymentStatus();
  };

  const handleGoToDashboard = () => {
    router.push('/school-admin/dashboard');
  };

  const handleGoToBuyCredit = () => {
    router.push('/school-admin/buy-credit');
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center px-4">
      <div className="max-w-md w-full">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
          className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 text-center"
        >
          {/* Status Icon */}
          <div className="mb-6">
            {paymentStatus === 'checking' && (
              <div className="mx-auto w-16 h-16 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                <Loader2 className="h-8 w-8 text-blue-600 dark:text-blue-400 animate-spin" />
              </div>
            )}
            
            {paymentStatus === 'success' && (
              <div className="mx-auto w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
                <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
              </div>
            )}
            
            {paymentStatus === 'failed' && (
              <div className="mx-auto w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center">
                <AlertCircle className="h-8 w-8 text-red-600 dark:text-red-400" />
              </div>
            )}
            
            {paymentStatus === 'pending' && (
              <div className="mx-auto w-16 h-16 bg-yellow-100 dark:bg-yellow-900/20 rounded-full flex items-center justify-center">
                <CreditCard className="h-8 w-8 text-yellow-600 dark:text-yellow-400" />
              </div>
            )}
          </div>

          {/* Status Message */}
          <div className="mb-6">
            {paymentStatus === 'checking' && (
              <>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                  Vérification du paiement...
                </h1>
                <p className="text-gray-600 dark:text-gray-400">
                  Nous vérifions le statut de votre paiement. Veuillez patienter.
                </p>
                {retryCount > 0 && (
                  <p className="text-sm text-gray-500 dark:text-gray-500 mt-2">
                    Tentative {retryCount}/10
                  </p>
                )}
              </>
            )}
            
            {paymentStatus === 'success' && (
              <>
                <h1 className="text-2xl font-bold text-green-600 dark:text-green-400 mb-2">
                  Paiement réussi !
                </h1>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  Votre achat de crédits a été traité avec succès.
                </p>
                {purchaseDetails && (
                  <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 text-left">
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Crédits achetés:</span>
                        <span className="font-semibold text-gray-900 dark:text-white">
                          {purchaseDetails.credits_purchased}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Montant total:</span>
                        <span className="font-semibold text-gray-900 dark:text-white">
                          {purchaseDetails.total_amount?.toLocaleString()} FCFA
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">ID de transaction:</span>
                        <span className="font-mono text-xs text-gray-900 dark:text-white">
                          {purchaseDetails.transaction_id}
                        </span>
                      </div>
                    </div>
                  </div>
                )}
              </>
            )}
            
            {paymentStatus === 'failed' && (
              <>
                <h1 className="text-2xl font-bold text-red-600 dark:text-red-400 mb-2">
                  Paiement échoué
                </h1>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  {error || 'Une erreur est survenue lors du traitement de votre paiement.'}
                </p>
                {transaction_id && (
                  <p className="text-xs text-gray-500 dark:text-gray-500 font-mono">
                    ID: {transaction_id}
                  </p>
                )}
              </>
            )}
            
            {paymentStatus === 'pending' && (
              <>
                <h1 className="text-2xl font-bold text-yellow-600 dark:text-yellow-400 mb-2">
                  Paiement en cours
                </h1>
                <p className="text-gray-600 dark:text-gray-400">
                  Votre paiement est en cours de traitement. Cela peut prendre quelques minutes.
                </p>
                {error && (
                  <p className="text-sm text-red-600 dark:text-red-400 mt-2">
                    {error}
                  </p>
                )}
              </>
            )}
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            {paymentStatus === 'success' && (
              <button
                onClick={handleGoToDashboard}
                className="w-full bg-blue-600 dark:bg-blue-500 text-white py-3 px-4 rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors flex items-center justify-center"
              >
                Aller au tableau de bord
                <ArrowRight className="h-4 w-4 ml-2" />
              </button>
            )}
            
            {(paymentStatus === 'failed' || (paymentStatus === 'pending' && error)) && (
              <>
                <button
                  onClick={handleRetry}
                  className="w-full bg-blue-600 dark:bg-blue-500 text-white py-3 px-4 rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors flex items-center justify-center"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Vérifier à nouveau
                </button>
                <button
                  onClick={handleGoToBuyCredit}
                  className="w-full border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 py-3 px-4 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  Essayer un nouvel achat
                </button>
              </>
            )}
            
            <button
              onClick={handleGoToDashboard}
              className="w-full text-gray-600 dark:text-gray-400 py-2 px-4 rounded-lg hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
            >
              Retour au tableau de bord
            </button>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
