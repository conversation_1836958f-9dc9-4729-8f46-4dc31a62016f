
const axios = require('axios');

// Configuration Fapshi
const baseUrl = process.env.FAPSHI_BASE_URL || 'https://sandbox.fapshi.com';
const headers = {
    apiuser: process.env.FAPSHI_API_USER || 'e3f28598-9248-49bc-84c6-ca491e94b25c',
    apikey: process.env.FAPSHI_API_KEY || 'FAK_TEST_c61281fedbd87d30e06d'
};

// Validation de la configuration
function validateConfig() {
    if (!headers.apiuser || !headers.apikey) {
        throw new Error('Fapshi API credentials are not properly configured');
    }
}

module.exports = {
    /** 
    *This function returns an object with the link were a user is to be redirected in order to complete his payment

    *Below is a parameter template. Just amount is required

        data = {
            "amount": Integer ,
            "email": String,
            "userId": String,
            "externalId": String,
            "redirectUrl": String,
            "message": String
        }
    */
    initiatePay(data){
        return new Promise(async function(resolve){
            try {
                validateConfig();

                // Validation des paramètres requis
                if(!data?.amount) {
                    resolve(error('amount required', 400));
                    return;
                }
                if(!Number.isInteger(data.amount)) {
                    resolve(error('amount must be of type integer', 400));
                    return;
                }
                if(data.amount < 100) {
                    resolve(error('amount cannot be less than 100 XAF', 400));
                    return;
                }

                // Validation des paramètres optionnels
                if(data.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
                    resolve(error('invalid email format', 400));
                    return;
                }

                if(data.userId && typeof data.userId !== 'string') {
                    resolve(error('userId must be a string', 400));
                    return;
                }

                if(data.externalId && typeof data.externalId !== 'string') {
                    resolve(error('externalId must be a string', 400));
                    return;
                }

                if(data.redirectUrl && typeof data.redirectUrl !== 'string') {
                    resolve(error('redirectUrl must be a string', 400));
                    return;
                }

                if(data.message && typeof data.message !== 'string') {
                    resolve(error('message must be a string', 400));
                    return;
                }

                const config = {
                    method: 'post',
                    url: baseUrl+'/initiate-pay',
                    headers: headers,
                    data: data
                }
                const response = await axios(config)
                response.data.statusCode = response.status
                resolve(response.data)
            }catch(e){
                const errorResponse = e.response?.data || { message: e.message };
                errorResponse.statusCode = e?.response?.status || 500;
                resolve(errorResponse);
            }
        })
    },
    
    /** 
    *This function directly initiates a payment request to a user's mobile device and 
    returns an object with a transId property that is used to get the status of the payment

    *Below is a parameter template. amount and phone are required

        data = {
            "amount": Integer ,
            "phone": String ,
            "medium": String,
            "name": String,
            "email": String,
            "userId": String,
            "externalId": String,
            "message": String
        }
    */
    directPay(data){
        return new Promise(async function(resolve){
            try {
                validateConfig();

                // Validation des paramètres requis
                if(!data?.amount) {
                    resolve(error('amount required', 400));
                    return;
                }
                if(!Number.isInteger(data.amount)) {
                    resolve(error('amount must be of type integer', 400));
                    return;
                }
                if(data.amount < 100) {
                    resolve(error('amount cannot be less than 100 XAF', 400));
                    return;
                }
                if(!data?.phone) {
                    resolve(error('phone number required', 400));
                    return;
                }
                if(typeof data.phone !== 'string') {
                    resolve(error('phone must be of type string', 400));
                    return;
                }
                // Validation améliorée du numéro de téléphone (Cameroun)
                if(!/^6[0-9]{8}$/.test(data.phone)) {
                    resolve(error('invalid phone number format. Must be 9 digits starting with 6', 400));
                    return;
                }

                // Validation des paramètres optionnels
                if(data.medium && !['mobile money', 'orange money'].includes(data.medium)) {
                    resolve(error('medium must be "mobile money" or "orange money"', 400));
                    return;
                }

                if(data.name && typeof data.name !== 'string') {
                    resolve(error('name must be a string', 400));
                    return;
                }

                if(data.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
                    resolve(error('invalid email format', 400));
                    return;
                }

                if(data.userId && typeof data.userId !== 'string') {
                    resolve(error('userId must be a string', 400));
                    return;
                }

                if(data.externalId && typeof data.externalId !== 'string') {
                    resolve(error('externalId must be a string', 400));
                    return;
                }

                if(data.message && typeof data.message !== 'string') {
                    resolve(error('message must be a string', 400));
                    return;
                }

                const config = {
                    method: 'post',
                    url: baseUrl+'/direct-pay',
                    headers: headers,
                    data: data
                }
                const response = await axios(config)
                response.data.statusCode = response.status
                resolve(response.data)
            }catch(e){
                const errorResponse = e.response?.data || { message: e.message };
                errorResponse.statusCode = e?.response?.status || 500;
                resolve(errorResponse);
            }
        })
    },

    /** 
    * This function returns an object containing the details of the transaction associated with the Id passed as parameter
    */
    paymentStatus(transId){
        return new Promise(async function(resolve){
            try {
                validateConfig();

                if(!transId || typeof transId !== 'string') {
                    resolve(error('invalid type, string expected', 400));
                    return;
                }
                // Validation améliorée de l'ID de transaction
                if(!/^[a-zA-Z0-9]{6,15}$/.test(transId)) {
                    resolve(error('invalid transaction id format', 400));
                    return;
                }

                const config = {
                    method: 'get',
                    url: baseUrl+'/payment-status/'+transId,
                    headers: headers
                }
                const response = await axios(config)
                response.data.statusCode = response.status
                resolve(response.data)
            }catch(e){
                const errorResponse = e.response?.data || { message: e.message };
                errorResponse.statusCode = e?.response?.status || 500;
                resolve(errorResponse);
            }
        })
    },
    
    /** 
    * This function expires the transaction associated with the Id passed as parameter and returns an object containing the details of the transaction
    */
    expirePay(transId){
        return new Promise(async function(resolve){
            try {
                validateConfig();

                if(!transId || typeof transId !== 'string') {
                    resolve(error('invalid type, string expected', 400));
                    return;
                }
                // Validation améliorée de l'ID de transaction
                if(!/^[a-zA-Z0-9]{6,15}$/.test(transId)) {
                    resolve(error('invalid transaction id format', 400));
                    return;
                }

                const config = {
                    method: 'post',
                    url: baseUrl+'/expire-pay',
                    data: {transId},
                    headers: headers
                }
                const response = await axios(config)
                response.data.statusCode = response.status
                resolve(response.data)
            }catch(e){
                const errorResponse = e.response?.data || { message: e.message };
                errorResponse.statusCode = e?.response?.status || 500;
                resolve(errorResponse);
            }
        })
    },
    
    /** 
    * This function returns an array of objects containing the transaction details of the user Id passed as parameter
    */
    userTrans(userId){
        return new Promise(async function(resolve){
            try {
                validateConfig();

                if(!userId || typeof userId !== 'string')
                    resolve(error('invalid type, string expected', 400))
                if(!/^[a-zA-Z0-9-_]{1,100}$/.test(userId))
                    resolve(error('invalid user id', 400))

                const config = {
                    method: 'get',
                    url: baseUrl+'/transaction/'+userId,
                    headers: headers
                }
                const response = await axios(config)
                response.data.statusCode = response.status
                resolve(response.data)
            }catch(e){
                e.response.data.statusCode = e?.response?.status
                resolve(e.response.data)
            }
        })
    },

    /**
     * Recherche de transactions avec filtres
     * Permet de filtrer les transactions par statut, medium, dates, montant, etc.
     *
     * @param {Object} filters - Filtres de recherche
     * @param {string} [filters.status] - Statut: 'created', 'successful', 'failed', 'expired'
     * @param {string} [filters.medium] - Medium: 'mobile money', 'orange money'
     * @param {string} [filters.start] - Date de début (YYYY-MM-DD)
     * @param {string} [filters.end] - Date de fin (YYYY-MM-DD)
     * @param {number} [filters.amt] - Montant exact
     * @param {number} [filters.limit] - Nombre max de résultats (1-100, défaut: 10)
     * @param {string} [filters.sort] - Ordre de tri: 'asc' ou 'desc' (défaut: desc)
     */
    searchTransactions(filters = {}){
        return new Promise(async function(resolve){
            try {
                validateConfig();

                // Validation des filtres
                if (filters.status && !['created', 'successful', 'failed', 'expired'].includes(filters.status)) {
                    resolve(error('invalid status filter', 400));
                    return;
                }

                if (filters.medium && !['mobile money', 'orange money'].includes(filters.medium)) {
                    resolve(error('invalid medium filter', 400));
                    return;
                }

                if (filters.start && !/^\d{4}-\d{2}-\d{2}$/.test(filters.start)) {
                    resolve(error('invalid start date format, use YYYY-MM-DD', 400));
                    return;
                }

                if (filters.end && !/^\d{4}-\d{2}-\d{2}$/.test(filters.end)) {
                    resolve(error('invalid end date format, use YYYY-MM-DD', 400));
                    return;
                }

                if (filters.amt && (!Number.isInteger(filters.amt) || filters.amt < 0)) {
                    resolve(error('amount must be a positive integer', 400));
                    return;
                }

                if (filters.limit && (!Number.isInteger(filters.limit) || filters.limit < 1 || filters.limit > 100)) {
                    resolve(error('limit must be an integer between 1 and 100', 400));
                    return;
                }

                if (filters.sort && !['asc', 'desc'].includes(filters.sort)) {
                    resolve(error('sort must be "asc" or "desc"', 400));
                    return;
                }

                // Construction des paramètres de requête
                const queryParams = new URLSearchParams();
                Object.keys(filters).forEach(key => {
                    if (filters[key] !== undefined && filters[key] !== null) {
                        queryParams.append(key, filters[key]);
                    }
                });

                const config = {
                    method: 'get',
                    url: `${baseUrl}/search${queryParams.toString() ? '?' + queryParams.toString() : ''}`,
                    headers: headers
                }

                const response = await axios(config);
                response.data.statusCode = response.status;
                resolve(response.data);
            }catch(e){
                const errorResponse = e.response?.data || { message: e.message };
                errorResponse.statusCode = e?.response?.status || 500;
                resolve(errorResponse);
            }
        })
    },

    /**
     * Récupère le solde actuel du compte de service
     * Retourne le solde et la devise du compte
     */
    getServiceBalance(){
        return new Promise(async function(resolve){
            try {
                validateConfig();

                const config = {
                    method: 'get',
                    url: baseUrl + '/balance',
                    headers: headers
                }

                const response = await axios(config);
                response.data.statusCode = response.status;
                resolve(response.data);
            }catch(e){
                const errorResponse = e.response?.data || { message: e.message };
                errorResponse.statusCode = e?.response?.status || 500;
                resolve(errorResponse);
            }
        })
    },

    /**
     * Valide un webhook Fapshi
     * Vérifie que le webhook provient bien de Fapshi en interrogeant l'API
     *
     * @param {Object} webhookData - Données du webhook reçu
     * @param {string} webhookData.transId - ID de transaction
     */
    validateWebhook(webhookData){
        return new Promise(async function(resolve){
            try {
                validateConfig();

                if (!webhookData || !webhookData.transId) {
                    resolve(error('webhook data must contain transId', 400));
                    return;
                }

                // Vérifier la transaction auprès de Fapshi pour valider le webhook
                const transactionStatus = await module.exports.paymentStatus(webhookData.transId);

                if (transactionStatus.statusCode !== 200) {
                    resolve(error('invalid webhook: transaction not found', 400));
                    return;
                }

                // Comparer les données importantes
                const isValid =
                    transactionStatus.transId === webhookData.transId &&
                    transactionStatus.status === webhookData.status &&
                    transactionStatus.amount === webhookData.amount;

                if (!isValid) {
                    resolve(error('webhook data does not match transaction status', 400));
                    return;
                }

                resolve({
                    valid: true,
                    transaction: transactionStatus,
                    statusCode: 200
                });

            }catch(e){
                const errorResponse = e.response?.data || { message: e.message };
                errorResponse.statusCode = e?.response?.status || 500;
                resolve(errorResponse);
            }
        })
    }

}

function error(message, statusCode){
    return {message, statusCode}
}