// Test rapide du service de notifications de paiement
const PaymentNotificationService = require('./src/services/paymentNotificationService');

async function testPaymentNotifications() {
  console.log('🧪 Test du service de notifications de paiement...');

  try {
    // Test notification de succès
    console.log('\n📧 Test notification de succès...');
    const successResult = await PaymentNotificationService.sendPaymentSuccessNotification({
      email: '<EMAIL>',
      phone: '+237698074525',
      userName: 'Test User',
      schoolName: 'École Test',
      creditsAmount: 10,
      totalAmount: 5000,
      currency: 'XAF',
      transactionId: 'TEST123456',
      purchaseDate: new Date().toLocaleDateString('fr-FR')
    });

    console.log('✅ Résultat notification succès:', {
      success: successResult.success,
      email: successResult.email?.success ? 'Envoyé' : 'Échec',
      sms: successResult.sms?.success ? 'Envoyé' : 'Échec'
    });

    // Test notification d'échec
    console.log('\n❌ Test notification d\'échec...');
    const failureResult = await PaymentNotificationService.sendPaymentFailureNotification({
      email: '<EMAIL>',
      phone: '+237698074525',
      userName: 'Test User',
      schoolName: 'École Test',
      creditsAmount: 10,
      totalAmount: 5000,
      currency: 'XAF',
      transactionId: 'TEST123456',
      failureReason: 'Fonds insuffisants',
      canRetry: true
    });

    console.log('✅ Résultat notification échec:', {
      success: failureResult.success,
      email: failureResult.email?.success ? 'Envoyé' : 'Échec',
      sms: failureResult.sms?.success ? 'Envoyé' : 'Échec'
    });

    // Test notification d'expiration
    console.log('\n⏰ Test notification d\'expiration...');
    const expirationResult = await PaymentNotificationService.sendPaymentExpirationNotification({
      email: '<EMAIL>',
      phone: '+237698074525',
      userName: 'Test User',
      schoolName: 'École Test',
      creditsAmount: 10,
      totalAmount: 5000,
      currency: 'XAF',
      transactionId: 'TEST123456'
    });

    console.log('✅ Résultat notification expiration:', {
      success: expirationResult.success,
      email: expirationResult.email?.success ? 'Envoyé' : 'Échec',
      sms: expirationResult.sms?.success ? 'Envoyé' : 'Échec'
    });

    // Test notification solde faible
    console.log('\n⚠️ Test notification solde faible...');
    const lowCreditsResult = await PaymentNotificationService.sendLowCreditsNotification({
      email: '<EMAIL>',
      phone: '+237698074525',
      userName: 'Test User',
      schoolName: 'École Test',
      remainingCredits: 5,
      threshold: 10,
      recommendedPurchase: 50
    });

    console.log('✅ Résultat notification solde faible:', {
      success: lowCreditsResult.success,
      email: lowCreditsResult.email?.success ? 'Envoyé' : 'Échec',
      sms: lowCreditsResult.sms?.success ? 'Envoyé' : 'Échec'
    });

    console.log('\n🎉 Tests terminés avec succès !');

  } catch (error) {
    console.error('❌ Erreur lors des tests:', error);
  }
}

// Exécuter le test si ce fichier est appelé directement
if (require.main === module) {
  testPaymentNotifications();
}

module.exports = testPaymentNotifications;
