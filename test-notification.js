// Test rapide du service de notifications
const NotificationService = require('./src/services/notificationService');

async function testNotifications() {
  console.log('🧪 Test du service de notifications...');

  try {
    // Test notification de succès
    const successResult = await NotificationService.sendPaymentSuccessNotification({
      email: '<EMAIL>',
      phone: '+237123456789',
      userName: 'Test User',
      schoolName: 'École Test',
      creditsAmount: 10,
      totalAmount: 5000,
      currency: 'XAF',
      transactionId: 'TEST123456',
      purchaseDate: new Date().toLocaleDateString('fr-FR')
    });

    console.log('✅ Test notification succès:', successResult);

    // Test notification d'échec
    const failureResult = await NotificationService.sendPaymentFailureNotification({
      email: '<EMAIL>',
      phone: '+237123456789',
      userName: 'Test User',
      schoolName: 'École Test',
      creditsAmount: 10,
      totalAmount: 5000,
      currency: 'XAF',
      transactionId: 'TEST123456',
      failureReason: 'Fonds insuffisants',
      canRetry: true
    });

    console.log('✅ Test notification échec:', failureResult);

  } catch (error) {
    console.error('❌ Erreur test notifications:', error);
  }
}

// Exécuter le test si ce fichier est appelé directement
if (require.main === module) {
  testNotifications();
}

module.exports = testNotifications;
