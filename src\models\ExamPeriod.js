const mongoose = require('mongoose');

const examPeriodSchema = new mongoose.Schema({
  exam_period_id: {
    type: String,
    required: true,
    unique: true
  },
  
  // School and Term references
  school_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'School',
    required: true
  },
  term_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Term',
    required: true
  },
  
  // Exam period details
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  description: {
    type: String,
    trim: true,
    maxlength: 500
  },
  
  // Time frame for the exam period
  start_date: {
    type: Date,
    required: true
  },
  end_date: {
    type: Date,
    required: true
  },
  
  // Exam type (mid-term, final, quiz, etc.)
  exam_type: {
    type: String,
    enum: ['mid_term', 'final', 'quiz', 'test', 'assessment', 'other'],
    default: 'test'
  },
  
  // Status
  status: {
    type: String,
    enum: ['draft', 'scheduled', 'active', 'completed', 'cancelled'],
    default: 'draft'
  },
  
  // Academic year
  academic_year: {
    type: String,
    required: true
  },

  // Session year (from academic year context)
  session_year: {
    type: String,
    required: true
  },
  
  // Priority level (higher number = higher priority)
  priority: {
    type: Number,
    default: 100, // Exams have higher priority than normal classes (which would be 50)
    min: 1,
    max: 200
  },
  
  // Settings
  settings: {
    // Allow normal classes during exam period
    allow_normal_classes: {
      type: Boolean,
      default: false
    },
    // Automatically suspend normal classes during this period
    suspend_normal_classes: {
      type: Boolean,
      default: true
    },
    // Notification settings
    notify_teachers: {
      type: Boolean,
      default: true
    },
    notify_students: {
      type: Boolean,
      default: true
    }
  },
  
  // Metadata
  created_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updated_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  // Notes
  notes: {
    type: String,
    maxlength: 1000
  }
}, {
  timestamps: true
});

// Indexes
examPeriodSchema.index({ school_id: 1, term_id: 1 });
examPeriodSchema.index({ school_id: 1, academic_year: 1 });
examPeriodSchema.index({ start_date: 1, end_date: 1 });
examPeriodSchema.index({ status: 1 });

// Validation: end_date must be after start_date
examPeriodSchema.pre('save', function(next) {
  if (this.end_date <= this.start_date) {
    next(new Error('End date must be after start date'));
  } else {
    next();
  }
});

// Generate unique exam_period_id before saving
examPeriodSchema.pre('save', async function(next) {
  if (!this.exam_period_id) {
    const { ensureUniqueId } = require('../utils/generateId');
    this.exam_period_id = await ensureUniqueId(this.constructor, 'exam_period_id', 'EXP');
  }
  next();
});

// Static method to check if a date falls within any active exam period
examPeriodSchema.statics.getActiveExamPeriod = async function(schoolId, date, termId = null) {
  const query = {
    school_id: schoolId,
    status: { $in: ['scheduled', 'active'] },
    start_date: { $lte: date },
    end_date: { $gte: date }
  };
  
  if (termId) {
    query.term_id = termId;
  }
  
  return await this.findOne(query)
    .populate('term_id', 'name term_type')
    .sort({ priority: -1 }); // Get highest priority exam period
};

// Static method to get all exam periods for a term
examPeriodSchema.statics.getByTerm = async function(schoolId, termId) {
  return await this.find({
    school_id: schoolId,
    term_id: termId
  })
    .populate('term_id', 'name term_type start_date end_date')
    .populate('created_by', 'first_name last_name name')
    .sort({ start_date: 1 });
};

// Static method to check for conflicts with existing exam periods
examPeriodSchema.statics.checkConflicts = async function(schoolId, startDate, endDate, excludeId = null) {
  const query = {
    school_id: schoolId,
    $or: [
      {
        start_date: { $lte: endDate },
        end_date: { $gte: startDate }
      }
    ]
  };
  
  if (excludeId) {
    query._id = { $ne: excludeId };
  }
  
  return await this.find(query)
    .populate('term_id', 'name term_type');
};

// Instance method to activate exam period
examPeriodSchema.methods.activate = async function() {
  this.status = 'active';
  await this.save();
  
  // If suspend_normal_classes is true, we could add logic here to suspend normal classes
  if (this.settings.suspend_normal_classes) {
    // This could trigger a background job to handle normal class suspension
    console.log(`🔔 Exam period ${this.name} activated - normal classes should be suspended`);
  }
  
  return this;
};

// Instance method to complete exam period
examPeriodSchema.methods.complete = async function() {
  this.status = 'completed';
  await this.save();
  
  console.log(`✅ Exam period ${this.name} completed`);
  return this;
};

module.exports = mongoose.model('ExamPeriod', examPeriodSchema);
