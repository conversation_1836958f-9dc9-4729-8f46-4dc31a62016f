// Script de test pour l'endpoint des moyennes de classes
// Ce script peut être utilisé pour tester le nouvel endpoint

const BASE_URL = 'http://localhost:5000/api'; // Ajustez selon votre configuration

console.log('🧪 Test de l\'endpoint des moyennes de classes...\n');

console.log('📋 ENDPOINT CRÉÉ:');
console.log('   Route: GET /api/grades/school/:school_id/class-averages');
console.log('   Fichier: D:/Projet/scholarify/backend/src/routes/gradeRoutes.js');
console.log('   Contrôleur: D:/Projet/scholarify/backend/src/controllers/gradeController.js');
console.log('   Méthode: getClassAverages');
console.log('');

console.log('🔧 PARAMÈTRES SUPPORTÉS:');
console.log('   - term_id (optionnel): ID du terme pour filtrer');
console.log('   - sequence_number (optionnel): Numéro de séquence (1-6)');
console.log('   - academic_year (optionnel): <PERSON><PERSON> a<PERSON> (ex: "2024-2025")');
console.log('');

console.log('📊 RÉPONSE ATTENDUE:');
console.log(`{
  "class_averages": [
    {
      "class_id": "ObjectId",
      "class_name": "6ème A",
      "average_grade": 14.75,
      "student_count": 25,
      "total_grades": 150
    },
    {
      "class_id": "ObjectId", 
      "class_name": "5ème B",
      "average_grade": 13.20,
      "student_count": 28,
      "total_grades": 168
    }
  ],
  "total_classes": 2,
  "filters_applied": {
    "term_id": "ObjectId ou null",
    "sequence_number": 1,
    "academic_year": "2024-2025"
  },
  "message": "Class averages retrieved successfully"
}`);
console.log('');

console.log('🔍 EXEMPLES D\'UTILISATION:');
console.log('');

console.log('1. Toutes les classes (sans filtre):');
console.log('   GET /api/grades/school/SCHOOL_ID/class-averages');
console.log('');

console.log('2. Filtré par terme:');
console.log('   GET /api/grades/school/SCHOOL_ID/class-averages?term_id=TERM_ID');
console.log('');

console.log('3. Filtré par terme et séquence:');
console.log('   GET /api/grades/school/SCHOOL_ID/class-averages?term_id=TERM_ID&sequence_number=1');
console.log('');

console.log('4. Filtré par année académique:');
console.log('   GET /api/grades/school/SCHOOL_ID/class-averages?academic_year=2024-2025');
console.log('');

console.log('🔐 AUTHENTIFICATION REQUISE:');
console.log('   - Token Bearer dans l\'en-tête Authorization');
console.log('   - Rôles autorisés: admin, super, school_admin, dean_of_studies');
console.log('   - Middleware: authenticate, checkSubscription, authorize, checkTeacherSchoolAccess');
console.log('');

console.log('⚙️ FONCTIONNEMENT INTERNE:');
console.log('   1. Filtre les grades par école et paramètres optionnels');
console.log('   2. Joint avec la table students pour obtenir class_id');
console.log('   3. Joint avec la table classes pour obtenir le nom de classe');
console.log('   4. Groupe par classe et calcule:');
console.log('      - Moyenne des scores (arrondie à 2 décimales)');
console.log('      - Nombre d\'étudiants uniques');
console.log('      - Nombre total de notes');
console.log('   5. Trie par moyenne décroissante');
console.log('   6. Retourne les résultats avec métadonnées');
console.log('');

console.log('🧪 POUR TESTER AVEC CURL:');
console.log('');
console.log('curl -X GET \\');
console.log('  "http://localhost:5000/api/grades/school/YOUR_SCHOOL_ID/class-averages" \\');
console.log('  -H "Authorization: Bearer YOUR_TOKEN" \\');
console.log('  -H "Content-Type: application/json"');
console.log('');

console.log('🧪 POUR TESTER AVEC POSTMAN:');
console.log('   1. Méthode: GET');
console.log('   2. URL: http://localhost:5000/api/grades/school/YOUR_SCHOOL_ID/class-averages');
console.log('   3. Headers:');
console.log('      - Authorization: Bearer YOUR_TOKEN');
console.log('      - Content-Type: application/json');
console.log('   4. Query Params (optionnels):');
console.log('      - term_id: YOUR_TERM_ID');
console.log('      - sequence_number: 1');
console.log('      - academic_year: 2024-2025');
console.log('');

console.log('⚠️  PRÉREQUIS POUR LES TESTS:');
console.log('   - Serveur backend démarré sur le port 5000');
console.log('   - Base de données MongoDB connectée');
console.log('   - Données de test:');
console.log('     * École avec school_id valide');
console.log('     * Étudiants assignés à des classes');
console.log('     * Notes (grades) enregistrées pour ces étudiants');
console.log('     * Termes et séquences configurés');
console.log('');

console.log('✅ ENDPOINT PRÊT À ÊTRE TESTÉ!');
console.log('   Le frontend peut maintenant utiliser ce service pour afficher');
console.log('   le graphique des top classes par moyennes avec filtres.');
