const CreditPurchase = require('../models/CreditPurchase');
const fapshi = require('../utils/fapshi');
const { sendEmail } = require('../utils/emailService');

/**
 * Service pour gérer les échecs de paiement et les tentatives de reprise
 */
class PaymentFailureService {
  
  /**
   * Traiter un échec de paiement
   * @param {string} transactionId - ID de la transaction échouée
   * @param {string} reason - Raison de l'échec
   * @param {Object} additionalData - Données supplémentaires sur l'échec
   */
  static async handlePaymentFailure(transactionId, reason, additionalData = {}) {
    try {
      console.log(`💔 Traitement échec de paiement: ${transactionId}`);

      // Trouver l'achat de crédit correspondant
      const creditPurchase = await CreditPurchase.findOne({
        $or: [
          { transaction_id: transactionId },
          { 'payment_gateway_response.transId': transactionId }
        ]
      }).populate('school_id purchased_by');

      if (!creditPurchase) {
        console.error(`❌ Achat de crédit non trouvé pour transaction: ${transactionId}`);
        return { success: false, error: 'Purchase not found' };
      }

      // Marquer comme échoué avec détails
      const failureData = {
        reason,
        timestamp: new Date(),
        error_code: additionalData.errorCode,
        error_details: additionalData.errorDetails,
        retry_count: creditPurchase.retry_count || 0
      };

      await creditPurchase.markAsFailed(reason, failureData);

      // Analyser le type d'échec pour déterminer les actions
      const failureAnalysis = this.analyzeFailureReason(reason, additionalData);

      // Envoyer notification appropriée
      await this.sendFailureNotification(creditPurchase, failureAnalysis);

      // Proposer des actions de récupération si applicable
      if (failureAnalysis.canRetry) {
        await this.scheduleRetryAttempt(creditPurchase, failureAnalysis);
      }

      console.log(`✅ Échec de paiement traité: ${transactionId}`);
      return { 
        success: true, 
        analysis: failureAnalysis,
        purchase: creditPurchase 
      };

    } catch (error) {
      console.error(`❌ Erreur lors du traitement de l'échec: ${error.message}`);
      throw error;
    }
  }

  /**
   * Analyser la raison de l'échec pour déterminer les actions possibles
   * @param {string} reason - Raison de l'échec
   * @param {Object} additionalData - Données supplémentaires
   * @returns {Object} Analyse de l'échec
   */
  static analyzeFailureReason(reason, additionalData = {}) {
    const analysis = {
      type: 'unknown',
      canRetry: false,
      retryDelay: 0,
      userAction: 'contact_support',
      severity: 'medium',
      message: reason
    };

    const reasonLower = reason.toLowerCase();

    // Échecs liés au réseau ou temporaires
    if (reasonLower.includes('network') || 
        reasonLower.includes('timeout') || 
        reasonLower.includes('temporary') ||
        reasonLower.includes('server error')) {
      analysis.type = 'network';
      analysis.canRetry = true;
      analysis.retryDelay = 5 * 60 * 1000; // 5 minutes
      analysis.userAction = 'retry_automatic';
      analysis.severity = 'low';
      analysis.message = 'Problème de réseau temporaire. Nouvelle tentative automatique prévue.';
    }

    // Échecs liés aux fonds insuffisants
    else if (reasonLower.includes('insufficient') || 
             reasonLower.includes('balance') ||
             reasonLower.includes('funds')) {
      analysis.type = 'insufficient_funds';
      analysis.canRetry = true;
      analysis.retryDelay = 30 * 60 * 1000; // 30 minutes
      analysis.userAction = 'check_balance';
      analysis.severity = 'medium';
      analysis.message = 'Fonds insuffisants. Vérifiez votre solde et réessayez.';
    }

    // Échecs liés à la carte/compte
    else if (reasonLower.includes('card') || 
             reasonLower.includes('account') ||
             reasonLower.includes('blocked') ||
             reasonLower.includes('expired')) {
      analysis.type = 'account_issue';
      analysis.canRetry = false;
      analysis.userAction = 'update_payment_method';
      analysis.severity = 'high';
      analysis.message = 'Problème avec votre méthode de paiement. Veuillez la mettre à jour.';
    }

    // Échecs liés à la validation
    else if (reasonLower.includes('validation') || 
             reasonLower.includes('invalid') ||
             reasonLower.includes('format')) {
      analysis.type = 'validation';
      analysis.canRetry = false;
      analysis.userAction = 'correct_information';
      analysis.severity = 'medium';
      analysis.message = 'Informations de paiement invalides. Veuillez les corriger.';
    }

    // Échecs liés aux limites
    else if (reasonLower.includes('limit') || 
             reasonLower.includes('exceeded')) {
      analysis.type = 'limit_exceeded';
      analysis.canRetry = true;
      analysis.retryDelay = 24 * 60 * 60 * 1000; // 24 heures
      analysis.userAction = 'wait_or_contact';
      analysis.severity = 'medium';
      analysis.message = 'Limite de transaction dépassée. Réessayez plus tard ou contactez votre banque.';
    }

    // Échecs de sécurité
    else if (reasonLower.includes('security') || 
             reasonLower.includes('fraud') ||
             reasonLower.includes('suspicious')) {
      analysis.type = 'security';
      analysis.canRetry = false;
      analysis.userAction = 'contact_bank';
      analysis.severity = 'high';
      analysis.message = 'Transaction bloquée pour des raisons de sécurité. Contactez votre banque.';
    }

    return analysis;
  }

  /**
   * Envoyer une notification d'échec appropriée
   * @param {Object} creditPurchase - Achat de crédit
   * @param {Object} failureAnalysis - Analyse de l'échec
   */
  static async sendFailureNotification(creditPurchase, failureAnalysis) {
    try {
      const school = creditPurchase.school_id;
      const user = creditPurchase.purchased_by;

      // Déterminer le template d'email selon le type d'échec
      let template = 'payment-failure';
      if (failureAnalysis.type === 'network') {
        template = 'payment-failure-network';
      } else if (failureAnalysis.type === 'insufficient_funds') {
        template = 'payment-failure-funds';
      } else if (failureAnalysis.type === 'account_issue') {
        template = 'payment-failure-account';
      }

      const emailData = {
        to: creditPurchase.purchaser_email,
        template,
        data: {
          userName: user?.name || creditPurchase.billing_info?.name || 'Cher client',
          schoolName: school?.name || 'Votre école',
          creditsAmount: creditPurchase.credits_purchased,
          totalAmount: creditPurchase.total_amount,
          currency: creditPurchase.currency,
          transactionId: creditPurchase.transaction_id,
          failureReason: failureAnalysis.message,
          canRetry: failureAnalysis.canRetry,
          userAction: failureAnalysis.userAction,
          retryUrl: `${process.env.FRONTEND_URL}/school-admin/buy-credit`,
          supportUrl: `${process.env.FRONTEND_URL}/support`,
          retryDelay: failureAnalysis.retryDelay
        }
      };

      await sendEmail(emailData);
      console.log(`📧 Notification d'échec envoyée à: ${creditPurchase.purchaser_email}`);

      // Envoyer notification interne si échec critique
      if (failureAnalysis.severity === 'high') {
        await this.sendInternalAlert(creditPurchase, failureAnalysis);
      }

    } catch (error) {
      console.error(`❌ Erreur envoi notification échec: ${error.message}`);
      // Ne pas faire échouer le processus principal pour un problème d'email
    }
  }

  /**
   * Programmer une tentative de reprise automatique
   * @param {Object} creditPurchase - Achat de crédit
   * @param {Object} failureAnalysis - Analyse de l'échec
   */
  static async scheduleRetryAttempt(creditPurchase, failureAnalysis) {
    try {
      const maxRetries = 3;
      const currentRetries = creditPurchase.retry_count || 0;

      if (currentRetries >= maxRetries) {
        console.log(`⚠️ Nombre maximum de tentatives atteint pour: ${creditPurchase.transaction_id}`);
        return;
      }

      // Programmer la reprise (ici on utilise setTimeout, en production utilisez un job queue)
      setTimeout(async () => {
        await this.attemptPaymentRetry(creditPurchase);
      }, failureAnalysis.retryDelay);

      console.log(`⏰ Tentative de reprise programmée dans ${failureAnalysis.retryDelay / 1000}s pour: ${creditPurchase.transaction_id}`);

    } catch (error) {
      console.error(`❌ Erreur programmation reprise: ${error.message}`);
    }
  }

  /**
   * Tenter une reprise de paiement
   * @param {Object} creditPurchase - Achat de crédit
   */
  static async attemptPaymentRetry(creditPurchase) {
    try {
      console.log(`🔄 Tentative de reprise pour: ${creditPurchase.transaction_id}`);

      // Incrémenter le compteur de tentatives
      creditPurchase.retry_count = (creditPurchase.retry_count || 0) + 1;
      await creditPurchase.save();

      // Vérifier d'abord le statut actuel avec Fapshi
      const currentStatus = await fapshi.paymentStatus(creditPurchase.transaction_id);

      if (currentStatus.statusCode === 200) {
        if (currentStatus.status === 'SUCCESSFUL') {
          console.log(`✅ Paiement finalement réussi lors de la vérification: ${creditPurchase.transaction_id}`);
          await creditPurchase.markAsCompleted(currentStatus);
          return { success: true, status: 'completed' };
        } else if (currentStatus.status === 'FAILED') {
          console.log(`❌ Paiement toujours échoué: ${creditPurchase.transaction_id}`);
          return { success: false, status: 'still_failed' };
        }
      }

      // Si le statut est toujours en attente, on peut essayer d'expirer et recréer
      // (Cette logique dépend de votre stratégie de reprise)
      
      return { success: false, status: 'retry_failed' };

    } catch (error) {
      console.error(`❌ Erreur lors de la tentative de reprise: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  /**
   * Envoyer une alerte interne pour les échecs critiques
   * @param {Object} creditPurchase - Achat de crédit
   * @param {Object} failureAnalysis - Analyse de l'échec
   */
  static async sendInternalAlert(creditPurchase, failureAnalysis) {
    try {
      const alertData = {
        to: process.env.ADMIN_EMAIL || '<EMAIL>',
        subject: `🚨 Échec de paiement critique - ${creditPurchase.transaction_id}`,
        template: 'internal-payment-alert',
        data: {
          transactionId: creditPurchase.transaction_id,
          schoolName: creditPurchase.school_id?.name,
          amount: creditPurchase.total_amount,
          failureType: failureAnalysis.type,
          failureReason: failureAnalysis.message,
          severity: failureAnalysis.severity,
          retryCount: creditPurchase.retry_count || 0,
          purchaserEmail: creditPurchase.purchaser_email,
          timestamp: new Date().toISOString()
        }
      };

      await sendEmail(alertData);
      console.log(`🚨 Alerte interne envoyée pour échec critique: ${creditPurchase.transaction_id}`);

    } catch (error) {
      console.error(`❌ Erreur envoi alerte interne: ${error.message}`);
    }
  }

  /**
   * Obtenir les statistiques des échecs de paiement
   * @param {Object} filters - Filtres pour les statistiques
   * @returns {Object} Statistiques des échecs
   */
  static async getFailureStatistics(filters = {}) {
    try {
      const { startDate, endDate, schoolId } = filters;
      
      const matchConditions = {
        payment_status: 'failed'
      };

      if (startDate && endDate) {
        matchConditions.created_at = {
          $gte: new Date(startDate),
          $lte: new Date(endDate)
        };
      }

      if (schoolId) {
        matchConditions.school_id = schoolId;
      }

      const stats = await CreditPurchase.aggregate([
        { $match: matchConditions },
        {
          $group: {
            _id: '$failure_data.reason',
            count: { $sum: 1 },
            totalAmount: { $sum: '$total_amount' },
            avgRetries: { $avg: '$retry_count' }
          }
        },
        { $sort: { count: -1 } }
      ]);

      const totalFailures = await CreditPurchase.countDocuments(matchConditions);
      const totalAmount = await CreditPurchase.aggregate([
        { $match: matchConditions },
        { $group: { _id: null, total: { $sum: '$total_amount' } } }
      ]);

      return {
        totalFailures,
        totalLostRevenue: totalAmount[0]?.total || 0,
        failuresByReason: stats,
        period: { startDate, endDate }
      };

    } catch (error) {
      console.error(`❌ Erreur calcul statistiques échecs: ${error.message}`);
      throw error;
    }
  }
}

module.exports = PaymentFailureService;
