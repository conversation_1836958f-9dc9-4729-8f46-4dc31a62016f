/**
 * Script pour créer une souscription par défaut pour une école
 * 
 * Ce script crée une souscription Basic avec 5 crédits gratuits pour une école donnée.
 * 
 * Usage: node src/scripts/createSchoolSubscription.js <school_id>
 */

const mongoose = require('mongoose');
const SchoolSubscription = require('../models/SchoolSubscription');
const School = require('../models/School');
require('dotenv').config();

async function createSchoolSubscription(schoolId) {
  try {
    // Connexion à MongoDB
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log('✅ Connexion à MongoDB établie');

    // Vérifier que l'école existe
    const school = await School.findById(schoolId);
    if (!school) {
      throw new Error(`École avec l'ID ${schoolId} non trouvée`);
    }

    console.log(`🏫 École trouvée: ${school.name}`);

    // Vérifier si une souscription existe déjà
    const existingSubscription = await SchoolSubscription.findOne({ school_id: schoolId });
    if (existingSubscription) {
      console.log('⚠️  Une souscription existe déjà pour cette école');
      console.log(`   Plan: ${existingSubscription.plan_type}`);
      console.log(`   Statut: ${existingSubscription.status}`);
      console.log(`   Crédits: ${existingSubscription.credits_balance}`);
      return existingSubscription;
    }

    // Créer une nouvelle souscription
    const subscription = await SchoolSubscription.createDefaultSubscription(schoolId);
    
    console.log('✅ Souscription créée avec succès !');
    console.log(`   ID: ${subscription._id}`);
    console.log(`   Plan: ${subscription.plan_type}`);
    console.log(`   Statut: ${subscription.status}`);
    console.log(`   Crédits gratuits: ${subscription.credits_balance}`);
    console.log(`   Fonctionnalités: ${subscription.features.join(', ')}`);

    return subscription;
    
  } catch (error) {
    console.error('❌ Erreur lors de la création de la souscription:', error);
    throw error;
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Connexion MongoDB fermée');
  }
}

// Fonction pour créer des souscriptions pour toutes les écoles sans souscription
async function createSubscriptionsForAllSchools() {
  try {
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log('✅ Connexion à MongoDB établie');

    // Trouver toutes les écoles
    const schools = await School.find({});
    console.log(`🏫 ${schools.length} écoles trouvées`);

    let created = 0;
    let existing = 0;

    for (const school of schools) {
      const existingSubscription = await SchoolSubscription.findOne({ school_id: school._id });
      
      if (!existingSubscription) {
        await SchoolSubscription.createDefaultSubscription(school._id);
        console.log(`✅ Souscription créée pour: ${school.name}`);
        created++;
      } else {
        console.log(`⚠️  Souscription existe déjà pour: ${school.name}`);
        existing++;
      }
    }

    console.log(`\n📊 Résumé:`);
    console.log(`   Souscriptions créées: ${created}`);
    console.log(`   Souscriptions existantes: ${existing}`);
    console.log(`   Total: ${schools.length}`);
    
  } catch (error) {
    console.error('❌ Erreur:', error);
    throw error;
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Connexion MongoDB fermée');
  }
}

// Exécuter le script si appelé directement
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('🔄 Création de souscriptions pour toutes les écoles...');
    createSubscriptionsForAllSchools()
      .then(() => {
        console.log('🎉 Terminé !');
        process.exit(0);
      })
      .catch((error) => {
        console.error('❌ Erreur:', error);
        process.exit(1);
      });
  } else {
    const schoolId = args[0];
    
    if (!mongoose.Types.ObjectId.isValid(schoolId)) {
      console.error('❌ ID d\'école invalide');
      process.exit(1);
    }
    
    console.log(`🔄 Création de souscription pour l'école: ${schoolId}`);
    createSchoolSubscription(schoolId)
      .then(() => {
        console.log('🎉 Terminé !');
        process.exit(0);
      })
      .catch((error) => {
        console.error('❌ Erreur:', error);
        process.exit(1);
      });
  }
}

module.exports = { 
  createSchoolSubscription, 
  createSubscriptionsForAllSchools 
};
