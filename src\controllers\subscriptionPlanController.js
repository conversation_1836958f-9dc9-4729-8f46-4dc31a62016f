const SubscriptionPlan = require('../models/SubscriptionPlan');
const SchoolSubscription = require('../models/SchoolSubscription');

// Obtenir tous les plans de souscription actifs
const getAllPlans = async (req, res) => {
  try {
    const plans = await SubscriptionPlan.getActivePlans();
    
    res.status(200).json({
      plans,
      message: 'Subscription plans retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching subscription plans:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Obtenir un plan spécifique par nom
const getPlanByName = async (req, res) => {
  try {
    const { plan_name } = req.params;

    if (!plan_name || !['basic', 'standard', 'custom'].includes(plan_name)) {
      return res.status(400).json({ message: 'Valid plan name is required' });
    }

    const plan = await SubscriptionPlan.getPlanByName(plan_name);
    if (!plan) {
      return res.status(404).json({ message: 'Plan not found' });
    }

    res.status(200).json({
      plan,
      message: 'Plan retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching plan:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Calculer le prix pour un nombre de crédits donné
const calculatePrice = async (req, res) => {
  try {
    const { plan_name, credits_amount } = req.query;

    if (!plan_name || !credits_amount) {
      return res.status(400).json({ 
        message: 'Plan name and credits amount are required' 
      });
    }

    const plan = await SubscriptionPlan.getPlanByName(plan_name);
    if (!plan) {
      return res.status(404).json({ message: 'Plan not found' });
    }

    const creditsNum = parseInt(credits_amount);
    if (creditsNum < plan.minimum_purchase) {
      return res.status(400).json({ 
        message: `Minimum purchase is ${plan.minimum_purchase} credits` 
      });
    }

    const subtotal = creditsNum * plan.price_per_credit;
    const discount = 0; // TODO: Implémenter la logique de réduction
    const total = subtotal - discount;

    res.status(200).json({
      calculation: {
        plan_name: plan.plan_name,
        credits_amount: creditsNum,
        price_per_credit: plan.price_per_credit,
        subtotal,
        discount,
        total,
        currency: 'XAF'
      },
      message: 'Price calculated successfully'
    });
  } catch (error) {
    console.error('Error calculating price:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Comparer les plans (pour la page pricing)
const comparePlans = async (req, res) => {
  try {
    const plans = await SubscriptionPlan.getActivePlans();
    
    // Organiser les données pour la comparaison
    const comparison = plans.map(plan => ({
      plan_name: plan.plan_name,
      display_name: plan.display_name,
      description: plan.description,
      price_per_credit: plan.price_per_credit,
      minimum_purchase: plan.minimum_purchase,
      chatbot_enabled: plan.chatbot_enabled,
      chatbot_cost_per_message: plan.chatbot_cost_per_message,
      features: plan.features,
      benefits: plan.benefits,
      limitations: plan.limitations,
      is_popular: plan.is_popular,
      color_theme: plan.color_theme,
      contact_info: plan.contact_info
    }));

    res.status(200).json({
      plans: comparison,
      message: 'Plans comparison retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching plans comparison:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Changer le plan d'une école
const changePlan = async (req, res) => {
  try {
    const { school_id } = req.params;
    const { new_plan_name } = req.body;

    if (!school_id || !new_plan_name) {
      return res.status(400).json({ 
        message: 'School ID and new plan name are required' 
      });
    }

    if (!['basic', 'standard', 'custom'].includes(new_plan_name)) {
      return res.status(400).json({ message: 'Invalid plan name' });
    }

    // Vérifier que le plan existe
    const newPlan = await SubscriptionPlan.getPlanByName(new_plan_name);
    if (!newPlan) {
      return res.status(404).json({ message: 'Plan not found' });
    }

    // Obtenir la souscription de l'école
    let subscription = await SchoolSubscription.findOne({ school_id });
    if (!subscription) {
      subscription = await SchoolSubscription.createDefaultSubscription(school_id);
    }

    const oldPlan = subscription.plan_type;
    
    // Mettre à jour le plan
    subscription.plan_type = new_plan_name;
    await subscription.save();

    res.status(200).json({
      subscription: {
        school_id: subscription.school_id,
        old_plan: oldPlan,
        new_plan: subscription.plan_type,
        features: subscription.features,
        credits_balance: subscription.credits_balance
      },
      message: 'Plan changed successfully'
    });
  } catch (error) {
    console.error('Error changing plan:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Obtenir les recommandations de plan pour une école
const getPlanRecommendations = async (req, res) => {
  try {
    const { school_id } = req.params;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    const subscription = await SchoolSubscription.findOne({ school_id });
    if (!subscription) {
      return res.status(404).json({ message: 'School subscription not found' });
    }

    // Analyser l'utilisation pour faire des recommandations
    const usageStats = await require('../models/CreditUsage').getUsageStats(school_id, 'month');
    const chatbotUsage = await require('../models/ChatbotUsage').getMonthlyUsage(
      school_id, 
      new Date().getFullYear(), 
      new Date().getMonth() + 1
    );

    let recommendations = [];

    // Logique de recommandation basée sur l'utilisation
    if (subscription.plan_type === 'basic') {
      if (chatbotUsage.length > 0 && chatbotUsage[0].total_messages > 0) {
        recommendations.push({
          plan: 'standard',
          reason: 'Vous utilisez déjà le chatbot. Le plan Standard vous donnerait un accès officiel.',
          potential_savings: 0,
          features_gained: ['Accès officiel au chatbot', 'Rapports avancés', 'Support prioritaire']
        });
      }
    }

    if (subscription.credits_used > 100) {
      recommendations.push({
        plan: 'custom',
        reason: 'Votre utilisation élevée pourrait bénéficier d\'un plan personnalisé.',
        potential_savings: 'À négocier',
        features_gained: ['Tarification personnalisée', 'Fonctionnalités sur mesure', 'Support dédié']
      });
    }

    if (recommendations.length === 0) {
      recommendations.push({
        plan: subscription.plan_type,
        reason: 'Votre plan actuel semble adapté à votre utilisation.',
        potential_savings: 0,
        features_gained: []
      });
    }

    res.status(200).json({
      current_plan: subscription.plan_type,
      usage_analysis: {
        monthly_credits_used: usageStats.reduce((sum, stat) => sum + stat.total_credits, 0),
        chatbot_messages: chatbotUsage.length > 0 ? chatbotUsage[0].total_messages : 0,
        efficiency_score: subscription.credits_used > 0 ? 
          Math.round((subscription.credits_used / subscription.credits_purchased) * 100) : 0
      },
      recommendations,
      message: 'Plan recommendations generated successfully'
    });
  } catch (error) {
    console.error('Error generating plan recommendations:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Initialiser les plans par défaut (pour l'administration)
const initializeDefaultPlans = async (req, res) => {
  try {
    await SubscriptionPlan.initializeDefaultPlans();
    
    const plans = await SubscriptionPlan.getActivePlans();
    
    res.status(200).json({
      plans,
      message: 'Default plans initialized successfully'
    });
  } catch (error) {
    console.error('Error initializing default plans:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

module.exports = {
  getAllPlans,
  getPlanByName,
  calculatePrice,
  comparePlans,
  changePlan,
  getPlanRecommendations,
  initializeDefaultPlans
};
