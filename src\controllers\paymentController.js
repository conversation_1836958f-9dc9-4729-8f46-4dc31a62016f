// controllers/webhookController.js
const fapshi = require('../utils/fapshi');
const mongoose = require('mongoose');
const moment = require('moment'); // Moment.js for date handling
const { ensureUniqueId } = require('../utils/generateId'); // Utility to generate unique IDs
const Subscription = require('../models/Subscription');
const User = require('../models/User');

async function handleWebhook(req, res) {
  try {
    console.log('Webhook received:', req.body);

    // Valider le webhook avec la nouvelle fonction
    const validation = await fapshi.validateWebhook(req.body);

    if (!validation.valid) {
      console.error('Invalid webhook:', validation.message);
      return res.status(400).json({ message: 'Invalid webhook' });
    }

    const event = validation.transaction;
    console.log('Valid webhook event:', event);

    const { transId, status, email, amount, dateInitiated, userId,externalId } = event;
    const subscriptionId = await ensureUniqueId(Subscription, 'subscription_id', 'SUB');
    const student_id = externalId.split("_");
    // Handle the event based on the status
    switch (status) {
      case 'SUCCESSFUL':
        // Calculate the expiry date for a yearly subscription
        const expiryDate = moment(dateInitiated).add(1, 'year').toDate();

        const subscriptionData = {
          transaction_id: transId,
          email: email,
          amount: amount,
          expiryDate: expiryDate,
          status: true, // Mark as active
          student_id:student_id,
          subscription_id: subscriptionId,
          guardian_id: userId, // Assuming userId is the guardian (optional, depending on your model)
        };

        // Check if subscription already exists with the same transaction_id
        let subscription = await Subscription.findOne({ guardian_id: userId });

        // If not, create a new subscription
        if (!subscription) {
          subscription = new Subscription(subscriptionData);
        } else {
          // Update the existing subscription (if necessary)
          subscription.expiryDate = expiryDate;
          subscription.transaction_id = transId,
          subscription.student_id = student_id,
          subscription.status = true; // Ensure the subscription is active
          subscription.subscription_id = subscriptionId; // Ensure unique subscription_id is added
        }

        // Save the subscription
        await subscription.save();
        break;

      case 'FAILED':
        // Mark subscription as inactive and log failure
        console.log(`Payment failed for transaction ${transId}`);
        let failedSubscription = await Subscription.findOne({ guardian_id: userId });

        if (failedSubscription) {
          subscription.transaction_id = transId,
          failedSubscription.status = false; 
          failedSubscription.expiryDate = null;
          await failedSubscription.save();
        }
        break;

      case 'EXPIRED':
        // Mark subscription as expired
        //console.log(`Payment expired for transaction ${transId}`);
        let expiredSubscription = await Subscription.findOne({ guardian_id: userId });

        if (expiredSubscription) {
          subscription.transaction_id = transId,
          expiredSubscription.status = false; 
          expiredSubscription.expiryDate = null;
          await expiredSubscription.save();
        } 
        break;

      default:
        console.log(`Unhandled event status: ${event.status}`);
    }

    // Return a 200 response to acknowledge receipt of the event
    res.send();
  } catch (error) {
    console.error('Error handling webhook:', error);
    res.status(500).send({ message: 'Internal Server Error' });
  }
}

const initiatePayment = async (req, res) => {
  try {
    const { userId, amount, email, externalId, redirectUrl } = req.body;

    // Validation des paramètres requis
    if (!userId || !amount) {
      return res.status(400).json({
        message: 'userId and amount are required'
      });
    }

    const objectId = new mongoose.Types.ObjectId(userId);

    // Vérifier si l'utilisateur existe et a les permissions appropriées
    const user = await User.findById(objectId);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Vérifier les permissions : admin ou parent (selon la mémoire)
    if (!['admin', 'parent'].includes(user.role)) {
      return res.status(403).json({
        message: 'You do not have permission to initiate a payment.'
      });
    }

    // Initier le paiement avec Fapshi
    const response = await fapshi.initiatePay({
      userId,
      amount,
      email,
      externalId,
      redirectUrl
    });

    // Vérifier la réponse de Fapshi
    if (response.statusCode !== 200) {
      return res.status(response.statusCode || 400).json({
        message: 'Payment initiation failed',
        error: response.message
      });
    }

    res.status(200).json(response);
  } catch (error) {
    console.error('Error handling payment:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
};

const checkPaymentStatus = async (req, res) => {
  try {
    const { transId } = req.params;

    // Validation du paramètre
    if (!transId) {
      return res.status(400).json({
        message: 'Transaction ID is required'
      });
    }

    const response = await fapshi.paymentStatus(transId);

    // Vérifier la réponse de Fapshi
    if (response.statusCode !== 200) {
      return res.status(response.statusCode || 400).json({
        message: 'Failed to get payment status',
        error: response.message
      });
    }

    res.status(200).json(response);
  } catch (error) {
    console.error('Error checking payment status:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
};
// Nouvelle fonction pour rechercher des transactions
const searchTransactions = async (req, res) => {
  try {
    const filters = req.query;

    // Vérifier les permissions (admin seulement pour la recherche)
    if (req.user && req.user.role !== 'admin') {
      return res.status(403).json({
        message: 'Admin access required for transaction search'
      });
    }

    const response = await fapshi.searchTransactions(filters);

    if (response.statusCode !== 200) {
      return res.status(response.statusCode || 400).json({
        message: 'Failed to search transactions',
        error: response.message
      });
    }

    res.status(200).json(response);
  } catch (error) {
    console.error('Error searching transactions:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
};

// Nouvelle fonction pour récupérer le solde du service
const getServiceBalance = async (req, res) => {
  try {
    // Vérifier les permissions (admin seulement)
    if (req.user && req.user.role !== 'admin') {
      return res.status(403).json({
        message: 'Admin access required for service balance'
      });
    }

    const response = await fapshi.getServiceBalance();

    if (response.statusCode !== 200) {
      return res.status(response.statusCode || 400).json({
        message: 'Failed to get service balance',
        error: response.message
      });
    }

    res.status(200).json(response);
  } catch (error) {
    console.error('Error getting service balance:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
};

// Nouvelle fonction pour expirer une transaction
const expireTransaction = async (req, res) => {
  try {
    const { transId } = req.params;

    if (!transId) {
      return res.status(400).json({
        message: 'Transaction ID is required'
      });
    }

    // Vérifier les permissions (admin ou propriétaire de la transaction)
    if (req.user && !['admin', 'parent'].includes(req.user.role)) {
      return res.status(403).json({
        message: 'Insufficient permissions to expire transaction'
      });
    }

    const response = await fapshi.expirePay(transId);

    if (response.statusCode !== 200) {
      return res.status(response.statusCode || 400).json({
        message: 'Failed to expire transaction',
        error: response.message
      });
    }

    res.status(200).json(response);
  } catch (error) {
    console.error('Error expiring transaction:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
};

module.exports = {
  handleWebhook,
  initiatePayment,
  checkPaymentStatus,
  searchTransactions,
  getServiceBalance,
  expireTransaction
};
